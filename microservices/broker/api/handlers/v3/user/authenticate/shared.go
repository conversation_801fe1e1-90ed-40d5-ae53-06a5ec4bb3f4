// Package v3 shared library of functions
package authenticate

import (
	"crypto/rand"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"time"

	jwttokens "synapse-its.com/shared/api/jwttokens"
	security "synapse-its.com/shared/api/security"
	connect "synapse-its.com/shared/connect"
	logger "synapse-its.com/shared/logger"
)

// For testing purposes
var randRead = rand.Read

func validateUsername(credentials credentials) error {
	if len(credentials.Username) < MinUsernameLength {
		logger.Debugf("username too short. min length (%d), got (%d)", MinUsernameLength, len(credentials.Username))
		return ErrAPIUnauthorized
	}
	if len(credentials.Username) > MaxUsernameLength {
		logger.Debugf("username too long. max length (%d), got (%d)", MaxUsernameLength, len(credentials.Username))
		return ErrAPIUnauthorized
	}

	return nil
}

func validatePassword(credentials credentials) error {
	if len(credentials.Password) < MinUserPasswordLength {
		logger.Debugf("password too short. min length (%d), got (%d)", MinUserPasswordLength, len(credentials.Password))
		return ErrAPIUnauthorized
	}
	if len(credentials.Password) > MaxUserPasswordLength {
		logger.Debugf("password too long. max length (%d), got (%d)", MaxUserPasswordLength, len(credentials.Password))
		return ErrAPIUnauthorized
	}

	return nil
}

func persistTokenToDB(pg connect.DatabaseExecutor, userID string, jwt string, expiresAt time.Time) error {
	query := `
		INSERT INTO
			{{UserToken}} (UserId, JWTToken, JWTTokenSha256, Created, Expiration)
		VALUES ($1, $2, $3, $4, $5)`
	_, err := pg.Exec(query, userID, jwt, security.CalculateSHA256(jwt), time.Now().UTC(), expiresAt)
	if err != nil {
		logger.Warnf("error inserting user token into database: (%v)", err)
		return err
	}
	return nil
}

func createUserPermissions(pg connect.DatabaseExecutor, userID string) (*jwttokens.UserPermissions, error) {
	userSoftwareGatewayAccess := []jwttokens.UserSoftwareGatewayAccess{}
	query := `
		SELECT
			sg.MachineKey
		FROM {{UserSoftwareGateway}} usg
		INNER JOIN {{SoftwareGateway}} sg
			ON usg.SoftwareGatewayId = sg.Id
		WHERE usg.userID = $1`
	err := pg.QueryGenericSlice(&userSoftwareGatewayAccess, query, userID)
	if err != nil {
		return nil, err
	}

	userPermissions := &jwttokens.UserPermissions{SoftwareGateway: userSoftwareGatewayAccess, Device: []jwttokens.UserDeviceAccess{}}
	return userPermissions, nil
}

func extractUserNameAndPassword(body io.ReadCloser) (string, string, error) {
	// Validate username and password are included in request body

	decoder := json.NewDecoder(body)

	var creds credentials

	err := decoder.Decode(&creds)
	if err != nil {
		logger.Debugf("error - (%v)", err)
		return "", "", errors.New("error parsing username and password")
	}

	// Validate the username and password min/max lengths are satisfied.
	err = validateUsername(creds)
	if err != nil {
		return "", "", err
	}
	err = validatePassword(creds)
	if err != nil {
		return "", "", err
	}

	return creds.Username, creds.Password, nil
}

// GenerateNonce generates a random nonce of n bytes
func generateNonce(n int) ([]byte, error) {
	nonce := make([]byte, n)
	_, err := randRead(nonce)
	if err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}
	return nonce, nil
}

func hasTokenExpired(expirationTimeStr string) (bool, error) {
	expirationTime, err := time.Parse(time.DateTime, expirationTimeStr)
	if err != nil {
		return true, err
	}
	currentTime := time.Now().UTC()
	if currentTime.After(expirationTime) {
		return true, errors.New("error - token passed in has expired")
	}

	return false, nil
}
