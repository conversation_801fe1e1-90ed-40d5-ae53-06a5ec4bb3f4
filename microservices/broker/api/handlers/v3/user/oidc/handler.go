package oidc

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/coreos/go-oidc"
	jwttokens "synapse-its.com/shared/api/jwttokens"
	SharedOidc "synapse-its.com/shared/api/oidc"
	"synapse-its.com/shared/api/response"
	security "synapse-its.com/shared/api/security"
	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/util"
)

// Global OIDC configurations
var (
	SynapseOIDC      *SharedOidc.OIDCConfig
	SynapseOIDCLocal *SharedOidc.OIDCConfig
)

// Dependencies for testing
type HandlerDeps struct {
	TokenExchanger         SharedOidc.OAuth2TokenExchanger
	ConnectionProvider     func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	JWTCreator             func(userName string, duration time.Duration, permissions jwttokens.UserPermissions) (string, time.Time, error)
	TokenPersister         func(pg connect.DatabaseExecutor, userID string, jwt string, expiresAt time.Time) error
	UserPermissionsCreator func(pg connect.DatabaseExecutor, userID string) (*jwttokens.UserPermissions, error)
	TimeProvider           func() time.Time
}

// NewHandlerDeps creates default dependencies
func NewHandlerDeps() HandlerDeps {
	return HandlerDeps{
		TokenExchanger:         SharedOidc.NewOAuth2TokenExchanger(),
		ConnectionProvider:     connect.GetConnections,
		JWTCreator:             jwttokens.CreateJwtTokenUsingDuration,
		TokenPersister:         persistTokenToDB,
		UserPermissionsCreator: createUserPermissions,
		TimeProvider:           time.Now,
	}
}

// InitializeOIDCConfigs initializes both production and local OIDC configurations
func InitializeOIDCConfigs() error {
	// Create base config from environment
	var err error
	SynapseOIDC, err = SharedOidc.NewConfigFromService(context.Background(), "Broker")
	if err != nil {
		return fmt.Errorf("failed to create base OIDC config: %w", err)
	}

	// Initialize local development config
	SynapseOIDCLocal, err = SharedOidc.InitializeLocalConfig(context.Background(), SynapseOIDC)
	if err != nil {
		return fmt.Errorf("failed to initialize local OIDC config: %w", err)
	}

	return nil
}

// LoginHandler handles OIDC login initiation
//
// @Summary      Initiate OIDC authentication
// @Description  Initiates the OIDC authentication flow by redirecting to the OIDC provider. This is a browser-based flow that requires the client to handle redirects. For API-only authentication without browser interaction, use the callback endpoint directly with an authorization code.
// @Tags         env:dev, env:qa, env:sandbox, user, oidc
// @Accept       json
// @Produce      json
// @Success      302   {string}  string  "Redirect to OIDC provider authorization URL"
// @Failure      500   {object}  shared.InternalServerErrorResponse  "Internal Server Error"
// @Router       /v3/user/oidc/login [get]
func LoginHandler(w http.ResponseWriter, r *http.Request) {
	handleLoginWithDeps(NewHandlerDeps())(w, r)
}

// CallbackHandler handles OIDC callback and returns JWT token
//
// @Summary      Complete OIDC authentication and get JWT token
// @Description  Completes the OIDC authentication flow by processing the authorization code and returning a JWT token. This endpoint can be called directly with an authorization code for API-only authentication without browser interaction.
// @Tags         env:dev, env:qa, env:sandbox, user, oidc
// @Accept       json
// @Produce      json
// @Param        code   query     string  true   "Authorization code from OIDC provider"
// @Param        state  query     string  true   "State parameter for CSRF protection"
// @Success      200    {object}  oidc.authResponse               "Authentication successful - returns user info and JWT token"
// @Failure      400    {object}  shared.BadRequestResponse       "Bad Request - invalid state or missing code"
// @Failure      401    {object}  shared.UnauthorizedResponse     "Unauthorized - invalid authorization code"
// @Failure      500    {object}  shared.InternalServerErrorResponse  "Internal Server Error"
// @Router       /v3/user/oidc/callback [get]
func CallbackHandler(w http.ResponseWriter, r *http.Request) {
	handleCallbackWithDeps(NewHandlerDeps())(w, r)
}

func handleLoginWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		state := util.RandomString(32)
		isDev := strings.HasPrefix(r.Host, "localhost:8080")

		// For API-only usage, return the authorization URL instead of redirecting
		if r.Header.Get("Accept") == "application/json" {
			oidcConfig := getConfig(isDev)
			authURL := oidcConfig.OAuth2Config.AuthCodeURL(state)

			response.CreateSuccessResponse(map[string]interface{}{
				"authorization_url": authURL,
				"state":             state,
			}, w)
			return
		}

		// Set the state cookie for browser-based flow
		http.SetCookie(w, &http.Cookie{
			Name:     "oauth_state",
			Value:    state,
			Path:     "/",
			HttpOnly: true,
			Secure:   !(isDev || strings.HasPrefix(r.Host, "broker:8080")),
			SameSite: http.SameSiteLaxMode,
		})

		// Choose the OIDC configuration based on the request host
		oidcConfig := getConfig(isDev)

		// Redirect to the OIDC provider's authorization endpoint
		http.Redirect(w, r, oidcConfig.OAuth2Config.AuthCodeURL(state), http.StatusFound)
	}
}

func handleCallbackWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := oidc.ClientContext(r.Context(), SharedOidc.LocalhostHTTPProxy)
		isDev := strings.HasPrefix(r.Host, "localhost:8080")

		// Get authorization code from query parameters
		code := r.URL.Query().Get("code")
		if code == "" {
			logger.Errorf("error - OAuth2Callback: missing authorization code")
			response.CreateBadRequestResponse(w)
			return
		}

		// For API-only usage, state validation is optional (can be passed as query param)
		stateParam := r.URL.Query().Get("state")

		// For browser-based flow, verify state cookie
		if r.Header.Get("Accept") != "application/json" {
			st, err := r.Cookie("oauth_state")
			if err != nil || stateParam != st.Value {
				logger.Errorf("error - OAuth2Callback: invalid state")
				response.CreateBadRequestResponse(w)
				return
			}

			// Delete the state cookie so it can't be reused
			http.SetCookie(w, &http.Cookie{
				Name:     "oauth_state",
				Value:    "",
				Path:     "/",
				HttpOnly: true,
				Secure:   !isDev,
				Expires:  time.Unix(0, 0),
				MaxAge:   -1,
				SameSite: http.SameSiteLaxMode,
			})
		}

		// Process OAuth2 callback and generate JWT
		authResp, err := processOAuth2CallbackLogic(ctx, code, isDev, deps)
		if err != nil {
			logger.Errorf("error - OAuth2Callback: %v", err.Error())
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Return JWT token response
		response.CreateAuthSuccessResponse(authResp, w)
	}
}

// processOAuth2CallbackLogic handles the OAuth2 token exchange and JWT generation
func processOAuth2CallbackLogic(ctx context.Context, code string, isDev bool, deps HandlerDeps) (*dataUserResponsePayload, error) {
	// Choose the OIDC configuration based on environment
	oidcConfig := getConfig(isDev)

	// Exchange code for token
	token, err := deps.TokenExchanger.Exchange(ctx, oidcConfig, code)
	if err != nil {
		return nil, ErrExchangeFailed
	}

	// Verify the ID Token and extract claims
	rawID, ok := token.Extra("id_token").(string)
	if !ok {
		return nil, ErrMissingIDToken
	}

	idToken, err := deps.TokenExchanger.VerifyIDToken(ctx, oidcConfig, rawID)
	if err != nil {
		return nil, ErrInvalidIDToken
	}

	// Extract claims from ID token
	claims, err := deps.TokenExchanger.ExtractClaims(idToken)
	if err != nil {
		return nil, ErrInvalidIDToken
	}

	// Extract user information from claims
	email, ok := claims["email"].(string)
	if !ok || email == "" {
		return nil, ErrMissingEmail
	}

	// Get database connections
	connections, err := deps.ConnectionProvider(ctx)
	if err != nil {
		return nil, ErrDatabaseConnection
	}
	pg := connections.Postgres

	// Find user by email in the database
	user, err := findUserByEmail(pg, email)
	if err != nil {
		return nil, err
	}

	// Update last login time
	err = updateLastLogin(pg, user.Id, deps.TimeProvider)
	if err != nil {
		logger.Warnf("failed to update last login for user %s: %v", user.Id, err)
		// Continue despite this error - it's not critical
	}

	// Get user permissions
	userPermissions, err := deps.UserPermissionsCreator(pg, user.Id)
	if err != nil {
		return nil, ErrUserPermissions
	}

	// Create JWT token (744 hours = 31 days, same as username/password auth)
	duration := time.Duration(744) * time.Hour
	jwt, expiresAt, err := deps.JWTCreator(email, duration, *userPermissions)
	if err != nil {
		return nil, ErrJWTCreation
	}

	// Persist token to database
	err = deps.TokenPersister(pg, user.Id, jwt, expiresAt)
	if err != nil {
		return nil, ErrTokenPersistence
	}

	// Create response payload
	userDetail := &userDetailRecord{
		UserID:         user.Orig_ID,
		UserIdentifier: user.Id,
		OrganizationID: user.OrganizationIdentifier,
		Username:       email,
		APIKey:         user.APIKey,
		Role:           "deprecated", // Same as username/password auth
	}

	return &dataUserResponsePayload{
		User:  *userDetail,
		Token: jwt,
	}, nil
}

func getConfig(isDev bool) *SharedOidc.OIDCConfig {
	if isDev {
		return SynapseOIDCLocal
	}
	return SynapseOIDC
}

// Helper functions

// findUserByEmail finds a user in the database by email address
func findUserByEmail(pg connect.DatabaseExecutor, email string) (*dbUser, error) {
	query := `
		SELECT
			u.Id,
			u.OrigID,
			744 as TokenDurationHours,
			m.OrganizationId as OrganizationIdentifier,
			u.Id as APIKey
		FROM {{User}} u
		LEFT JOIN {{AuthMethod}} am
			ON am.UserId = u.Id
		LEFT JOIN {{Memberships}} m
			ON m.AuthMethodId = am.Id
		WHERE am.username ILIKE $1 AND am.IsEnabled
		LIMIT 1
		`

	user := dbUser{}
	err := pg.QueryRowStruct(&user, query, email)

	switch {
	case errors.Is(err, sql.ErrNoRows):
		return nil, ErrUserNotFound
	case err != nil:
		return nil, ErrDatabaseQuery
	default:
		return &user, nil
	}
}

// updateLastLogin updates the user's last login timestamp
func updateLastLogin(pg connect.DatabaseExecutor, userID string, timeProvider func() time.Time) error {
	query := "UPDATE {{AuthMethod}} SET LastLogin = $1 WHERE UserId = $2"
	_, err := pg.Exec(query, timeProvider().UTC(), userID)
	return err
}

// persistTokenToDB persists the JWT token to the database
func persistTokenToDB(pg connect.DatabaseExecutor, userID string, jwt string, expiresAt time.Time) error {
	query := `
		INSERT INTO
			{{UserToken}} (UserId, JWTToken, JWTTokenSha256, Created, Expiration)
		VALUES ($1, $2, $3, $4, $5)`
	_, err := pg.Exec(query, userID, jwt, security.CalculateSHA256(jwt), time.Now().UTC(), expiresAt)
	if err != nil {
		logger.Warnf("error inserting user token into database: (%v)", err)
		return err
	}
	return nil
}

// createUserPermissions creates user permissions for JWT token
func createUserPermissions(pg connect.DatabaseExecutor, userID string) (*jwttokens.UserPermissions, error) {
	userSoftwareGatewayAccess := []jwttokens.UserSoftwareGatewayAccess{}
	query := `
		SELECT
			sg.MachineKey
		FROM {{UserSoftwareGateway}} usg
		INNER JOIN {{SoftwareGateway}} sg
			ON usg.SoftwareGatewayId = sg.Id
		WHERE usg.userID = $1`
	err := pg.QueryGenericSlice(&userSoftwareGatewayAccess, query, userID)
	if err != nil {
		return nil, err
	}

	userPermissions := &jwttokens.UserPermissions{
		SoftwareGateway: userSoftwareGatewayAccess,
		Device:          []jwttokens.UserDeviceAccess{},
	}
	return userPermissions, nil
}

// Legacy handler for backward compatibility
var Handler = LoginHandler
