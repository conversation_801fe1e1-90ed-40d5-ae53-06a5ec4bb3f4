package oidc

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/coreos/go-oidc"
	SharedOidc "synapse-its.com/shared/api/oidc"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/util"
)

type HandlerDeps struct{}

// Global OIDC configurations
var (
	SynapseOIDC      *SharedOidc.OIDCConfig
	SynapseOIDCLocal *SharedOidc.OIDCConfig
)

// InitializeOIDCConfigs initializes both production and local OIDC configurations
func InitializeOIDCConfigs() error {
	// Create base config from environment
	SynapseOIDC, err := SharedOidc.NewConfigFromService(context.Background(), "Broker")
	if err != nil {
		return fmt.Errorf("failed to create base OIDC config: %w", err)
	}

	// Initialize local development config
	SynapseOIDCLocal, err = SharedOidc.InitializeLocalConfig(context.Background(), SynapseOIDC)
	if err != nil {
		return fmt.Errorf("failed to initialize local OIDC config: %w", err)
	}

	return nil
}

func handleLoginWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		state := util.RandomString(32)
		isDev := strings.HasPrefix(r.Host, "localhost:8080")

		// Set the state cookie
		http.SetCookie(w, &http.Cookie{
			Name:     "oauth_state",
			Value:    state,
			Path:     "/",
			HttpOnly: true,
			Secure:   !(isDev || strings.HasPrefix(r.Host, "broker:8080")),
			SameSite: http.SameSiteLaxMode,
		})

		// Choose the OIDC configuration based on the request host
		oidcConfig := getConfig(isDev)

		// Redirect to the OIDC provider's authorization endpoint
		http.Redirect(w, r, oidcConfig.OAuth2Config.AuthCodeURL(state), http.StatusFound)
	}
}

func handleCallbackWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := oidc.ClientContext(r.Context(), SharedOidc.LocalhostHTTPProxy)
		isDev := strings.HasPrefix(r.Host, "localhost:8080")
		// Verify state cookie
		st, err := r.Cookie("oauth_state")
		if err != nil || r.URL.Query().Get("state") != st.Value {
			logger.Errorf("error - OAuth2Callback: invalid state")
			response.CreateBadRequestResponse(w)
			return
		}

		// Delete the state cookie so it can't be reused
		http.SetCookie(w, &http.Cookie{
			Name:     "oauth_state",
			Value:    "",
			Path:     "/",
			HttpOnly: true,
			Secure:   !isDev,
			Expires:  time.Unix(0, 0),
			MaxAge:   -1,
			SameSite: http.SameSiteLaxMode,
		})
	}
}

func getConfig(isDev bool) *SharedOidc.OIDCConfig {
	if isDev {
		return SynapseOIDCLocal
	}
	return SynapseOIDC
}

var Handler = handleLoginWithDeps(HandlerDeps{})
