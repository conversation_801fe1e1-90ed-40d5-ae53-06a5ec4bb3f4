package profile

import (
	"context"
	"database/sql"
	"errors"
	"net/http"

	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	UserPermissionsFromContext func(ctx context.Context) (*authorizer.UserPermissions, bool)
	GetConnections             func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	GetUserProfile             func(pg connect.DatabaseExecutor, userID string) (*UserProfileRecord, error)
	GetAppVersion              func(pg connect.DatabaseExecutor) (*AppVersions, error)
}

// HandlerWithDeps returns an http.HandlerFunc with injected deps.
//
// @Summary      Get current user profile
// @Description  Returns the authenticated user's profile and application version.
// @Tags         env:dev, env:qa, env:sandbox, user
// @Produce      json
// @Security     JWTAuth
// @Success      200  {object}  profile.ResponsePayload              "User profile retrieved successfully"
// @Failure      401  {object}  shared.UnauthorizedResponse         "Unauthorized"
// @Failure      500  {object}  shared.InternalServerErrorResponse  "Internal Server Error"
// @Router       /v3/user/profile [get]
func HandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get user info from jwt authorizer using dependency
		userPermissions, ok := deps.UserPermissionsFromContext(ctx)
		if !ok {
			logger.Error(ErrUserInfoRetrieve.Error())
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Get User Profile using dependency
		profile, err := deps.GetUserProfile(pg, userPermissions.UserID)
		if err != nil {
			logger.Errorf("Error getting user profile: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get App Version using dependency
		appVersion, err := deps.GetAppVersion(pg)
		if err != nil {
			logger.Errorf("Error getting app version: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		payload := ResponsePayload{
			User:       *profile,
			AppVersion: *appVersion,
		}
		response.CreateSuccessResponse(payload, w)
	}
}

// Handler is the production-ready HTTP handler using default dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	UserPermissionsFromContext: authorizer.UserPermissionsFromContext,
	GetConnections:             connect.GetConnections,
	GetUserProfile:             getUserProfile,
	GetAppVersion:              getAppVersion,
})

// getUserProfile retrieves user profile data from the database.
// Note: If a user has multiple auth methods with different emails, this function picks the first one found.
// The LIMIT 1 clause ensures we only get one record even if multiple auth methods exist.
var getUserProfile = func(pg connect.DatabaseExecutor, userID string) (*UserProfileRecord, error) {
	query := `
		SELECT 
			u.OrigID as id,
			u.Id as user_identifier,
			COALESCE(am.UserName, '') as username,
			COALESCE(u.FirstName, '') as firstname,
			COALESCE(u.LastName, '') as lastname,
			COALESCE(am.Email, '') as email,
			COALESCE(u.Mobile, '') as mobile,
			u.NotificationSmsEnabled as notificationsmsenabled,
			COALESCE(u.LastLogin, '1970-01-01'::timestamp) as lastloginutc,
			COALESCE(u.Description, '') as description
		FROM {{User}} u
		LEFT JOIN {{AuthMethod}} am ON u.Id = am.UserId
		WHERE u.Id = $1
		LIMIT 1`

	profile := &UserProfileRecord{}
	if err := pg.QueryRowStruct(profile, query, userID); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrProfileNotFound
		}
		return nil, err
	}
	return profile, nil
}

// getAppVersion retrieves the application version from the database.
// TODO: CAT-319 - Remove AppVersion from the user/profile endpoint when confirmed it is unneeded.
var getAppVersion = func(pg connect.DatabaseExecutor) (*AppVersions, error) {
	// Hardcoded app version to prevent app breakage
	version := &AppVersions{
		EDIFieldServiceApp: struct {
			Dev          AppVersion `json:"dev"`
			NotPublished AppVersion `json:"notpublished"`
			Published    AppVersion `json:"published"`
		}{
			Dev: AppVersion{
				Android: "2",
				IOS:     "2",
				Windows: "1",
			},
			NotPublished: AppVersion{
				Android: "2",
				IOS:     "8",
				Windows: "1",
			},
			Published: AppVersion{
				Android: "1",
				IOS:     "1",
				Windows: "1",
			},
		},
	}
	return version, nil
}
