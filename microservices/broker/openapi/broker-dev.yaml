basePath: /api
components: {}
definitions:
    authenticate.authResponse:
        properties:
            code:
                description: HTTP status code
                example: 200
                type: integer
            data:
                allOf:
                    - $ref: '#/definitions/authenticate.dataUserResponsePayload'
                description: Authentication data containing user info and JWT token
            message:
                description: Success message
                example: Authentication successful
                type: string
            status:
                description: Response status
                example: success
                type: string
        type: object
    authenticate.credentials:
        properties:
            password:
                description: User's password
                example: securePassword123
                type: string
            username:
                description: User's username or email address
                example: <EMAIL>
                type: string
        required:
            - password
            - username
        type: object
    authenticate.dataUserResponsePayload:
        properties:
            token:
                description: JWT token for authentication
                example: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
                type: string
            user:
                allOf:
                    - $ref: '#/definitions/authenticate.userDetailRecord'
                description: User information
        type: object
    authenticate.requestBody:
        properties:
            token:
                description: Authentication token (can be empty string for initial setup)
                example: abc123token
                type: string
        required:
            - token
        type: object
    authenticate.userDetailRecord:
        properties:
            api_key:
                description: API key (legacy field)
                example: api-key-789
                type: string
            organization_id:
                description: Organization UUID the user belongs to
                example: org-uuid-456
                type: string
            role:
                description: User's role within the organization
                example: admin
                type: string
            user_id:
                description: Unique user identifier
                example: 12345
                type: integer
            user_identifier:
                description: User UUID identifier
                example: user-uuid-123
                type: string
            username:
                description: Username/email used for login
                example: <EMAIL>
                type: string
        type: object
    device.channelStatus:
        properties:
            green:
                description: Green channel status array (true = on)
                items:
                    type: boolean
                type: array
            red:
                description: Red channel status array (true = on)
                items:
                    type: boolean
                type: array
            yellow:
                description: Yellow channel status array (true = on)
                items:
                    type: boolean
                type: array
        type: object
    device.dataPayload:
        properties:
            device_id:
                description: Numeric device identifier
                example: 12345
                type: integer
            device_identifier:
                description: Device UUID or identifier string
                example: device-uuid-123
                type: string
            device_info:
                allOf:
                    - $ref: '#/definitions/device.deviceMetadata'
                description: Device hardware and software metadata
            location:
                allOf:
                    - $ref: '#/definitions/device.location'
                description: Geographic location information
            status:
                allOf:
                    - $ref: '#/definitions/device.deviceStatus'
                description: Current device status and fault information
        type: object
    device.deviceMetadata:
        properties:
            application_version:
                description: Application software version
                example: ""
                type: string
            comm_version:
                description: Communication protocol version
                example: 3.0.1
                type: string
            device_type:
                description: Type classification of the device
                example: EDI_LEGACY
                type: string
            firmware_type:
                description: Firmware type classification
                example: ""
                type: string
            firmware_version:
                description: Firmware version number
                example: 1.5.2
                type: string
            ip_address:
                description: Device IP address
                example: *************
                type: string
            manufacturer:
                description: Device manufacturer name
                example: EDI
                type: string
            model:
                description: Device model identifier
                example: Model-X1
                type: string
            port:
                description: Device communication port
                example: "8080"
                type: string
            rms_engine_firmware_type:
                description: RMS engine firmware type
                example: 1.0.0
                type: string
            rms_engine_firmware_version:
                description: RMS engine firmware version
                example: 4.2.1
                type: string
            user_assigned_device_id:
                description: User-defined device identifier
                example: DEVICE001
                type: string
            user_assigned_device_name:
                description: User-friendly device name
                example: Main St
                type: string
        type: object
    device.deviceStatus:
        properties:
            faulted_channel_status:
                allOf:
                    - $ref: '#/definitions/device.channelStatus'
                description: The state of the channels of the device during the last fault
            heartbeat_received_utc:
                description: Timestamp of last heartbeat received
                example: "2024-01-15T10:30:00Z"
                type: string
            last_fault_reason:
                description: Description of the most recent fault
                example: Communication timeout
                type: string
            last_fault_uploaded_utc:
                description: Timestamp when the last fault was reported
                example: "2024-01-15T09:15:00Z"
                type: string
            log_uploaded_utc:
                description: Timestamp of last log upload
                example: "2024-01-15T10:25:00Z"
                type: string
            state:
                description: Current device state (nofault, fault, nevercomm, etc.)
                example: nofault
                type: string
        type: object
    device.location:
        properties:
            latitude:
                description: Device latitude coordinate
                example: "40.7128"
                type: string
            longitude:
                description: Device longitude coordinate
                example: "-74.0060"
                type: string
        type: object
    instruction.userInstructionRequest:
        properties:
            device_id:
                description: Device identifier (integer ID or UUID string format)
                items:
                    type: integer
                type: array
            instruction:
                description: Instruction command to send to device
                example: get_device_logs
                type: string
        required:
            - device_id
            - instruction
        type: object
    notifications.Request:
        properties:
            notification_sms_enabled:
                description: Enable or disable SMS notifications for the user
                example: true
                type: boolean
        required:
            - notification_sms_enabled
        type: object
    notifications.Response:
        properties:
            notification_sms_enabled:
                description: Current SMS notification setting after update
                example: true
                type: boolean
            trace_id:
                description: Request trace identifier for debugging
                example: trace-abc-456
                type: string
            user_id:
                description: User's unique identifier
                example: user-uuid-123
                type: string
        type: object
    profile.AppVersion:
        properties:
            android:
                type: string
            ios:
                type: string
            windows:
                type: string
        type: object
    profile.AppVersions:
        properties:
            EDIFieldServiceApp:
                properties:
                    dev:
                        $ref: '#/definitions/profile.AppVersion'
                    notpublished:
                        $ref: '#/definitions/profile.AppVersion'
                    published:
                        $ref: '#/definitions/profile.AppVersion'
                type: object
        type: object
    profile.ResponsePayload:
        properties:
            app_version:
                $ref: '#/definitions/profile.AppVersions'
            user_profile:
                $ref: '#/definitions/profile.UserProfileRecord'
        type: object
    profile.UserProfileRecord:
        properties:
            description:
                type: string
            email:
                type: string
            first_name:
                type: string
            last_login:
                type: string
            last_name:
                type: string
            mobile:
                type: string
            notification_sms_enabled:
                type: boolean
            user_id:
                type: integer
            user_identifier:
                type: string
            username:
                type: string
        type: object
    shared.BadRequestResponse:
        properties:
            code:
                description: HTTP status code
                example: 400
                type: integer
            data:
                description: Always null for error responses
            message:
                description: Error message
                example: Bad Request
                type: string
            status:
                description: Response status
                example: error
                type: string
        type: object
    shared.InternalServerErrorResponse:
        properties:
            code:
                description: HTTP status code
                example: 500
                type: integer
            data:
                description: Always null for error responses
            message:
                description: Error message
                example: Internal Server Error
                type: string
            status:
                description: Response status
                example: error
                type: string
        type: object
    shared.UnauthorizedResponse:
        properties:
            code:
                description: HTTP status code
                example: 401
                type: integer
            data:
                description: Always null for error responses
            message:
                description: Error message
                example: Unauthorized
                type: string
            status:
                description: Response status
                example: error
                type: string
        type: object
    softwaregateway.CloudSettings:
        properties:
            api_key:
                description: the api key used by the software gateway to communicate with the cloud
                type: string
            token:
                description: the rotating token used by the software gateway to authenticate with the cloud
                type: string
        type: object
    softwaregateway.DeviceSettings:
        properties:
            device_id:
                description: uniquely identifies this device amongst all possible devices
                type: string
            device_type:
                description: the type of device (EDI_LEGACY, EDI_NEXT_GEN, etc)
                type: string
            enable_realtime:
                description: flag indiciating whether or not to send realtime channel state to firebase
                type: string
            flush_connection_ms:
                description: the amount of time to wait to flush all bytes from the tcp connection for the device
                type: string
            ip_address:
                description: the device ip address
                type: string
            latitude:
                description: the device latitude
                type: string
            longitude:
                description: the device longitude
                type: string
            port:
                description: the device port
                type: string
        type: object
    softwaregateway.GlobalSettings:
        properties:
            aws:
                allOf:
                    - $ref: '#/definitions/softwaregateway.CloudSettings'
                description: all cloud settings
            devices:
                description: device info for all devices the software gateway communicates with
                items:
                    $ref: '#/definitions/softwaregateway.DeviceSettings'
                type: array
            gateway:
                additionalProperties: true
                description: all software gateway settings (dynamic)
                type: object
            google:
                description: google settings (aka firebase connection info) base 64 end encrypted
                type: string
            organization_id:
                description: the organization the software gateway is assigned to
                type: string
            public_key:
                description: the public key used to authenticate the signature of a jwt
                type: string
        type: object
    softwaregateway.OnDemandPayload:
        properties:
            device_identifier:
                type: string
            instruction:
                type: string
        type: object
    softwaregateway.SoftwareGatewayUpdateResponse:
        properties:
            config_update_available:
                type: string
            instructions:
                items:
                    $ref: '#/definitions/softwaregateway.OnDemandPayload'
                type: array
        type: object
    update.requestBody:
        properties:
            token:
                description: Authentication token for gateway verification
                example: abc123token
                type: string
        required:
            - token
        type: object
info:
    contact: {}
    description: Public endpoints for third-party integrations.
    title: Broker API
    version: "1.0"
openapi: ""
paths:
    /v3/data/device:
        get:
            description: Retrieves comprehensive device information including location, status, metadata, and real-time channel status. Users can query all authorized devices or a specific device by ID. The endpoint combines data from PostgreSQL (device configuration and fault history) with Redis (real-time status and heartbeat information).
            parameters:
                - description: Specific device ID to retrieve (integer ID or UUID). If omitted, returns all authorized devices
                  in: query
                  name: deviceid
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Device data retrieved successfully
                    schema:
                        items:
                            $ref: '#/definitions/device.dataPayload'
                        type: array
                "400":
                    description: Bad Request
                    schema:
                        $ref: '#/definitions/shared.BadRequestResponse'
                "401":
                    description: Unauthorized
                    schema:
                        $ref: '#/definitions/shared.UnauthorizedResponse'
                "500":
                    description: Internal Server Error
                    schema:
                        $ref: '#/definitions/shared.InternalServerErrorResponse'
            security:
                - JWTAuth: []
            summary: Get device data and status
            tags:
                - data
    /v3/data/fault:
        get:
            description: Retrieves comprehensive fault information including monitor reset logs, previous failures, configuration changes, AC line events, and fault signal sequences. The endpoint processes fault data and returns device-specific diagnostic information based on the monitor model (ECL2010, CMU2212, MMU16LE). Users must specify a device ID and have appropriate access permissions.
            parameters:
                - description: Device ID to retrieve fault data for (integer ID or UUID format)
                  in: query
                  name: deviceid
                  required: true
                  type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Device fault data retrieved successfully
                    schema:
                        items:
                            type: integer
                        type: array
                "400":
                    description: Bad Request
                    schema:
                        $ref: '#/definitions/shared.BadRequestResponse'
                "401":
                    description: Unauthorized
                    schema:
                        $ref: '#/definitions/shared.UnauthorizedResponse'
                "500":
                    description: Internal Server Error
                    schema:
                        $ref: '#/definitions/shared.InternalServerErrorResponse'
            security:
                - JWTAuth: []
            summary: Get device fault logs and diagnostics
            tags:
                - data
    /v3/gateway/authenticate:
        post:
            consumes:
                - application/json
            description: Authenticates a software gateway and returns configuration settings, device information, and cloud credentials
            parameters:
                - description: Gateway device identifier (MachineKey)
                  in: header
                  name: gateway-device-id
                  required: true
                  type: string
                - description: Message type (must be 'authenticate')
                  in: header
                  name: message-type
                  required: true
                  type: string
                - description: Gateway software version
                  in: header
                  name: gateway-version
                  type: string
                - description: Authentication request body
                  in: body
                  name: body
                  required: true
                  schema:
                    $ref: '#/definitions/authenticate.requestBody'
            produces:
                - application/json
            responses:
                "200":
                    description: Authentication successful
                    schema:
                        $ref: '#/definitions/softwaregateway.GlobalSettings'
                "401":
                    description: Unauthorized
                    schema:
                        $ref: '#/definitions/shared.UnauthorizedResponse'
                "500":
                    description: Internal Server Error
                    schema:
                        $ref: '#/definitions/shared.InternalServerErrorResponse'
            summary: Authenticate software gateway
            tags:
                - gateway
    /v3/gateway/ingest:
        post:
            consumes:
                - application/octet-stream
                - application/json
            description: Receives and processes various types of data from software gateways, validates authentication, and publishes to appropriate PubSub topics. Supports multiple message types including RMS data, fault logs, performance stats, and more.
            parameters:
                - description: Gateway device identifier (MachineKey)
                  in: header
                  name: gateway-device-id
                  required: true
                  type: string
                - description: Type of message being sent
                  enum:
                    - rmsData
                    - rmsEngine
                    - monitorName
                    - macAddress
                    - faultLogs
                    - perfStats
                    - gatewayLog
                    - faultNotification
                    - wrapperResponse
                  in: header
                  name: message-type
                  required: true
                  type: string
                - description: Message version (semantic versioning)
                  enum:
                    - v1
                  in: header
                  name: message-version
                  required: true
                  type: string
                - description: API key for gateway authentication
                  in: header
                  name: x-api-key
                  required: true
                  type: string
                - description: Gateway timezone
                  in: header
                  name: tz
                  type: string
                - description: Request host header
                  in: header
                  name: host
                  type: string
                - description: Request user agent
                  in: header
                  name: user-agent
                  type: string
                - description: Request content length
                  in: header
                  name: content-length
                  type: string
                - description: Request content type
                  in: header
                  name: content-type
                  type: string
                - description: Message payload (binary or JSON data depending on message type)
                  in: body
                  name: body
                  required: true
                  schema:
                    type: string
            produces:
                - application/json
            responses:
                "200":
                    description: Data successfully ingested
                    schema:
                        type: string
                "401":
                    description: Unauthorized
                    schema:
                        $ref: '#/definitions/shared.UnauthorizedResponse'
                "500":
                    description: Internal Server Error
                    schema:
                        $ref: '#/definitions/shared.InternalServerErrorResponse'
            summary: Ingest gateway data
            tags:
                - gateway
    /v3/gateway/update:
        post:
            consumes:
                - application/json
            description: Retrieves pending instructions and configuration updates for a software gateway. This endpoint is used by gateways to check for new device instructions and configuration changes. Instructions are marked as received when fetched and will not be returned again.
            parameters:
                - description: Gateway device identifier (MachineKey)
                  in: header
                  name: gateway-device-id
                  required: true
                  type: string
                - description: Message type (must be 'config')
                  in: header
                  name: message-type
                  required: true
                  type: string
                - description: Update request body with authentication token
                  in: body
                  name: body
                  required: true
                  schema:
                    $ref: '#/definitions/update.requestBody'
            produces:
                - application/json
            responses:
                "200":
                    description: Updates and instructions retrieved successfully
                    schema:
                        $ref: '#/definitions/softwaregateway.SoftwareGatewayUpdateResponse'
                "401":
                    description: Unauthorized
                    schema:
                        $ref: '#/definitions/shared.UnauthorizedResponse'
                "500":
                    description: Internal Server Error
                    schema:
                        $ref: '#/definitions/shared.InternalServerErrorResponse'
            summary: Get gateway updates and instructions
            tags:
                - gateway
    /v3/user/account/close:
        post:
            description: Permanently closes the authenticated user's account by marking it as deleted and removing all associated authentication tokens. This action is irreversible - once an account is closed, the user will no longer be able to authenticate or access any system resources. All active JWT tokens for the user are immediately invalidated and removed from the database.
            produces:
                - application/json
            responses:
                "200":
                    description: Account closed successfully
                    schema:
                        type: string
                "401":
                    description: Unauthorized
                    schema:
                        $ref: '#/definitions/shared.UnauthorizedResponse'
                "500":
                    description: Internal Server Error
                    schema:
                        $ref: '#/definitions/shared.InternalServerErrorResponse'
            security:
                - JWTAuth: []
            summary: Close user account
            tags:
                - user
    /v3/user/account/notifications:
        post:
            consumes:
                - application/json
            description: Updates the authenticated user's SMS notification preferences. This endpoint allows users to enable or disable SMS notifications for their account. The setting is stored in the user's profile and affects whether they receive SMS alerts for device events, fault notifications, and other system alerts.
            parameters:
                - description: Notification preferences update request
                  in: body
                  name: body
                  required: true
                  schema:
                    $ref: '#/definitions/notifications.Request'
            produces:
                - application/json
            responses:
                "200":
                    description: Notification preferences updated successfully
                    schema:
                        $ref: '#/definitions/notifications.Response'
                "400":
                    description: Bad Request
                    schema:
                        $ref: '#/definitions/shared.BadRequestResponse'
                "401":
                    description: Unauthorized
                    schema:
                        $ref: '#/definitions/shared.UnauthorizedResponse'
                "500":
                    description: Internal Server Error
                    schema:
                        $ref: '#/definitions/shared.InternalServerErrorResponse'
            security:
                - JWTAuth: []
            summary: Update user notification preferences
            tags:
                - user
    /v3/user/authenticate:
        post:
            consumes:
                - application/json
            description: Authenticates a user with username and password credentials. On successful authentication, returns user information and a JWT token that must be used in the 'jwt-token' header for accessing protected endpoints. The JWT token expires after 744 hours (31 days) and contains user permissions and organization information.
            parameters:
                - description: User authentication credentials
                  in: body
                  name: body
                  required: true
                  schema:
                    $ref: '#/definitions/authenticate.credentials'
            produces:
                - application/json
            responses:
                "200":
                    description: Authentication successful - returns user info and JWT token
                    schema:
                        $ref: '#/definitions/authenticate.authResponse'
                "401":
                    description: Unauthorized - invalid credentials
                    schema:
                        $ref: '#/definitions/shared.UnauthorizedResponse'
                "500":
                    description: Internal Server Error
                    schema:
                        $ref: '#/definitions/shared.InternalServerErrorResponse'
            summary: Authenticate user and get JWT token
            tags:
                - user
    /v3/user/instruction:
        post:
            consumes:
                - application/json
            description: Sends a management instruction to a specific device. Users must have device management permissions to send instructions. Currently supports the 'get_device_logs' instruction. The instruction is queued for execution by the device gateway and processed asynchronously.
            parameters:
                - description: Device instruction request
                  in: body
                  name: body
                  required: true
                  schema:
                    $ref: '#/definitions/instruction.userInstructionRequest'
            produces:
                - application/json
            responses:
                "200":
                    description: Instruction queued successfully
                    schema:
                        type: string
                "400":
                    description: Bad Request
                    schema:
                        $ref: '#/definitions/shared.BadRequestResponse'
                "401":
                    description: Unauthorized
                    schema:
                        $ref: '#/definitions/shared.UnauthorizedResponse'
                "500":
                    description: Internal Server Error
                    schema:
                        $ref: '#/definitions/shared.InternalServerErrorResponse'
            security:
                - JWTAuth: []
            summary: Send instruction to device
            tags:
                - user
    /v3/user/profile:
        get:
            description: Returns the authenticated user's profile and application version.
            produces:
                - application/json
            responses:
                "200":
                    description: User profile retrieved successfully
                    schema:
                        $ref: '#/definitions/profile.ResponsePayload'
                "401":
                    description: Unauthorized
                    schema:
                        $ref: '#/definitions/shared.UnauthorizedResponse'
                "500":
                    description: Internal Server Error
                    schema:
                        $ref: '#/definitions/shared.InternalServerErrorResponse'
            security:
                - JWTAuth: []
            summary: Get current user profile
            tags:
                - user
schemes:
    - https
securityDefinitions:
    JWTAuth:
        in: header
        name: jwt-token
        type: apiKey
swagger: "2.0"
