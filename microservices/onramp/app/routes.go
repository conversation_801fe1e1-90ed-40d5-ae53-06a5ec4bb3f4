package app

import (
	"github.com/gorilla/mux"

	"synapse-its.com/onramp/middlewares"
	"synapse-its.com/shared/api/middleware"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/httplogger"
)

// NewRouter initializes the router and registers routes.
func NewRouter(connections *connect.Connections, batch bqbatch.Batcher) *mux.Router {
	router := mux.NewRouter()

	// Apply middleware globally (e.g., logging, authentication).
	router.Use(middleware.ConnectionsMiddleware(connections))
	router.Use(middleware.BQBatchMiddleware(batch))
	router.Use(httplogger.LoggingMiddleware)
	router.Use(middlewares.PanicMiddleware)

	// Define default endpoints
	// Note: Assets handler removed as part of migration from SPA to WEB authentication

	// Note: Protected routes are set up in App.Serve() where we have access to the auth handler's session store

	return router
}
