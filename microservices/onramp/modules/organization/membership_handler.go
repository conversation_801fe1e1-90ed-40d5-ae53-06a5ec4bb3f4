package organization

import (
	"encoding/json"
	"net/http"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/logger"
	organizationDomain "synapse-its.com/shared/rest/domain/organization"
	"synapse-its.com/shared/rest/onramp/helper"
)

func (h *Handler) getUsersByOrganizationID(w http.ResponseWriter, r *http.Request) {
	organizationID, err := helper.ParseUUIDFromRequest(r, "organizationId")
	if err != nil {
		logger.Errorf("Error parsing organization ID: %v", err)
		response.CreateBadRequestResponse(w)
		return
	}

	users, err := h.membershipBiz.GetUsersByOrganizationID(organizationID)
	if err != nil {
		logger.Errorf("Error getting users by organization ID: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	response.CreateSuccessResponse(users, w)
}

func (h *<PERSON><PERSON>) updateUserRolesInOrganization(w http.ResponseWriter, r *http.Request) {
	orgId, err := helper.ParseUUIDFromRequest(r, "organizationId")
	if err != nil {
		logger.Errorf("Error parsing organization ID: %v", err)
		response.CreateBadRequestResponse(w)
		return
	}

	userId, err := helper.ParseUUIDFromRequest(r, "userId")
	if err != nil {
		logger.Errorf("Error parsing user ID: %v", err)
		response.CreateBadRequestResponse(w)
		return
	}

	var respBody struct {
		RoleIDs []uuid.UUID `json:"roleIds"`
	}
	err = json.NewDecoder(r.Body).Decode(&respBody)
	if err != nil {
		logger.Errorf("Error decoding request body: %v", err)
		response.CreateBadRequestResponse(w)
		return
	}

	err = h.membershipBiz.UpdateUserRolesInOrganization(userId, orgId, respBody.RoleIDs)
	if err != nil {
		if err == organizationDomain.ErrUserNotFound {
			logger.Errorf("User not found in organization: %v", err)
			response.CreateNotFoundResponse(w)
			return
		}
		logger.Errorf("Error updating user role in organization: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	response.CreateSuccessResponse(nil, w)
}

func (h *Handler) deleteUserFromOrganization(w http.ResponseWriter, r *http.Request) {
	orgId, err := helper.ParseUUIDFromRequest(r, "organizationId")
	if err != nil {
		logger.Errorf("Error parsing organization ID: %v", err)
		response.CreateBadRequestResponse(w)
		return
	}

	userId, err := helper.ParseUUIDFromRequest(r, "userId")
	if err != nil {
		logger.Errorf("Error parsing user ID: %v", err)
		response.CreateBadRequestResponse(w)
		return
	}

	err = h.membershipBiz.DeleteUserFromOrganization(userId, orgId)
	if err != nil {
		logger.Errorf("Error deleting user from organization: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	response.CreateSuccessResponse(nil, w)
}
