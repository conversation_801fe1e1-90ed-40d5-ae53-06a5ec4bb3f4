# Rush Hour (RH) - Developer Implementation Guide

**Development and Testing Guide** - All code examples are based on tested, working implementations validated through end-to-end testing and ready for integration development.

## Overview

Rush Hour (RH) is a horizontally-scalable Go microservice for real-time communication between:

- **Gateways** (each proxying \~500 devices)
- **FSA clients** (Field Service App, Onramp UI)

It uses **Socket.IO** with a **Redis adapter** for scalable real-time messaging. Messages are routed based on envelope metadata and are serialized using **Protobuf**.

---

## Critical Authentication Requirements

**Authentication has been a common source of integration issues. Follow these requirements exactly:**

#### Onramp Authentication (Pending)

Onramp client authentication is not yet implemented and is intentionally omitted at this time. Current namespaces:

- `/auth/fsa` for Field Service App (FSA)
- `/auth/gateway` for Gateways

Onramp authentication will be introduced in a future update with its own guidance.

### FSA Client Authentication (JWT)
```javascript
// Simplified example: focuses on transport and auth; production code should add retries and error handling.
// CORRECT: Auth data in the Socket.IO auth object
const socket = io("https://<rushhour domain name>/auth/fsa", {
  transports: ["websocket", "polling"],  // WebSocket preferred, polling fallback
  auth: {
    token: jwt_token  // Must be "token" key in auth object
  }
});

socket.on("auth_success", (data) => {
  console.log("FSA authenticated:", data);
  // Ready for device communication
});
```

### Gateway Client Authentication (Machine Key + API Key)
```javascript
// Simplified example: focuses on transport and auth; production code should add retries and error handling.
// CORRECT: Auth data in the Socket.IO auth object  
const socket = io("https://<rushhour domain name>/auth/gateway", {
  transports: ["websocket", "polling"],  // WebSocket preferred, polling fallback
  auth: {
    machine_key: "your_machine_key",
    api_key: "your_api_key"
  }
});

socket.on("gateway_init", (data) => {
  console.log("Gateway authenticated:", data);
  // Ready for device communication
});
```

### Common Authentication Mistakes

**DO NOT use query parameters for auth:**
```javascript
// WRONG: Auth in query parameters (will fail)
const socket = io("https://<rushhour domain name>/auth/fsa?token=" + jwt_token);
```

**Transport Configuration Note:**
```javascript
// Recommended: WebSocket with polling fallback
const socket = io("https://<rushhour domain name>/auth/fsa", {
  transports: ["websocket", "polling"]
});
```

**Authentication Event Differences:**
- FSA clients receive `auth_success` event upon successful authentication
- Gateway clients receive `gateway_init` event upon successful authentication
- Allow 10-15 seconds for authentication timeout handling

#### Empty Token Behavior (FSA clarification)

- The FSA `auth` object must include a `token` key. If `token` is present but empty or invalid, the server will emit `error` with `"unauthorized"` and disconnect the socket.
- The Socket.IO `connect` event can occur before authentication has completed. Clients must rely on `auth_success` to confirm authentication rather than `connect`.

---

## Technology Stack

- **Language**: Go
- **Socket.IO Library**: [`github.com/zishang520/socket.io`](https://github.com/zishang520/socket.io) (Redis adapter supported)
- **Serialization**: Protobuf
- **Scaling**: Redis pub/sub adapter + Kubernetes

---

## Communication Architecture

### Direct Socket Messaging

RH uses **Socket.IO Session IDs** for direct 1:1 communication between clients and gateways:

- **Device Commands**: FSA -> RH -> Gateway (direct socket messaging)
- **Device Responses**: Gateway -> RH -> FSA (direct socket messaging)  
- **Command Routing**: RH enriches envelopes with missing UUIDs, Session IDs, Gateway IDs

### Streaming Rooms (Broadcasting)

Rooms are used only for broadcasting streaming data to multiple viewers:

| Room                                             | Purpose                                            |
| ------------------------------------------------ | -------------------------------------------------- |
| `org:<org_id>:device:<device_id>:stream_display` | Broadcasts visual display frames from devices     |
| `org:<org_id>:device:<device_id>:stream_rms`     | Broadcasts RMS sensor data from devices           |

> **Key Benefits**: 
> - **Efficient 1:1 routing** via direct socket messaging
> - **Scalable broadcasting** via rooms for streaming data  
> - **No unnecessary room joins** for simple device commands

### Gateway stream control (direct socket)

RushHour instructs gateways to start or stop producing streaming data for a specific device and stream type via direct socket messaging. The gateway does not join Socket.IO rooms. Instead, RH emits a `stream_control` event to the target gateway socket in the `/auth/gateway` namespace (which the gateway used to connect to RushHour):

```json
{
  "action": "start_stream" | "end_stream",
  "device_id": "<device_id>",
  "stream_type": "stream_display" | "stream_rms"
}
```

Gateways act on these commands to start/stop streaming accordingly. For streaming, gateways emit stream-specific events to RH:

- `stream_display` for display frames
- `stream_rms` for RMS data

RH enriches and broadcasts to the appropriate stream room(s). For 1:1 responses, gateways continue to use `device_message`, which must include `SessionId`.

---

## Message Envelope (Binary-Only)

To avoid base64 overhead for large messages, RH uses a **pure binary transport**. The outer SocketEnvelope is always a protobuf message serialized to bytes and contains routing metadata and the payload. Payload content can be either protobuf or JSON depending on `EnvelopeType`.

### Envelope (Protobuf)

```proto
enum EnvelopeType {
  ENVELOPE_UNKNOWN = 0;
  ENVELOPE_WRAPPER_COMMAND = 1;
  ENVELOPE_WRAPPER_RESPONSE = 2;
  ENVELOPE_COMMAND_JSON = 3;
  ENVELOPE_COMMAND_PROTOBUF = 4;
}

enum OriginType {
  ORIGIN_UNKNOWN = 0;
  ORIGIN_GATEWAY = 1;
  ORIGIN_FSA = 2;
  ORIGIN_RUSHHOUR = 3;
  ORIGIN_ONRAMP = 4;
}

message SocketEnvelope {
  EnvelopeType type = 1;
  uint32 request_id = 2;
  string session_id = 3;
  string user_id = 4;
  string device_id = 5;
  string organization_id = 6;
  OriginType origin = 7;
  bytes payload = 8;
}
```

> Socket.IO's binary event support allows sending `[]byte` directly.

### Transport Format

- Always send the entire `SocketEnvelope` as protobuf-serialized bytes (`[]byte`).

// Simplified example: envelope creation and binary emit; error handling omitted.
Example (protobuf SocketEnvelope):
```go
// Build envelope
env := &rushhourv1.SocketEnvelope{
  Type:      rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_COMMAND,
  RequestId: 123,
  DeviceId:  deviceID,
  Origin:    rushhourv1.OriginType_ORIGIN_FSA,
  Payload:   payloadBytes,
}
bin, _ := proto.Marshal(env)
client.Emit("device_request", bin)
```

### Payload Formats

- The `EnvelopeType` indicates how to interpret the inner `payload` bytes:
  - `ENVELOPE_WRAPPER_COMMAND` / `ENVELOPE_WRAPPER_RESPONSE`: payload is protobuf-encoded.  Payload is from the `monf` family of protobuf messages.
  - `ENVELOPE_COMMAND_PROTOBUF`: Not implemented yet and is for future development.
  - `ENVELOPE_COMMAND_JSON`: payload is JSON-encoded (arbitrary structure) and should be interpreted as UTF-8 JSON bytes.  There is no use case for this yet.

Notes:
- Clients must not send the outer `SocketEnvelope` as JSON; only the inner `payload` may be JSON when using the appropriate type.

Example (gateway streaming via SocketEnvelope):
```go
// Build WrapperResponse (inner payload)
wr := &monfwrappers.WrapperResponse{ /* fill fields and realtime data */ }
wrBytes, _ := proto.Marshal(wr)

// Wrap in rushhour SocketEnvelope (outer, always protobuf)
env := &rushhourv1.SocketEnvelope{
  Type:           rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_RESPONSE,
  RequestId:      reqID,
  SessionId:      "", // empty because this is a streaming broadcast
  DeviceId:       deviceID,
  OrganizationId: orgID,
  Origin:         rushhourv1.OriginType_ORIGIN_GATEWAY,
  Payload:        wrBytes,
}
bin, _ := proto.Marshal(env)
client.Emit("device_message", bin)
```

#### SessionId Semantics

- RH assigns a SessionId for request/response routing. For commands originating from FSA/Onramp, RH sets/overwrites `SessionId` based on the caller's Socket.IO session ID.
- Any `SessionId` provided by FSA/Onramp is ignored and replaced by RH. Clients should not attempt to set it.
- Gateways must preserve the `SessionId` value from the received command when sending a response so RH can route it back to the originating FSA/Onramp.
- For broadcast streaming messages (from gateways), `SessionId` should be omitted; RH will fan out to stream rooms.

---

## RH Responsibilities

### 1. Namespace-Based Authentication

**Critical: RushHour uses namespace-based authentication with different handlers for FSA and Gateway clients.**

```go
// Simplified pseudocode: actual implementation uses zishang Socket.IO server namespaces and event handlers.
// FSA Authentication Namespace: /auth/fsa
server.Of("/auth/fsa", nil).On("connection", func(clients ...interface{}) {
    conn := clients[0].(*socket.Socket)

    // Extract and validate JWT (permissions and DB lookups omitted here)
    authMap, _ := conn.Handshake().Auth.(map[string]interface{})
    token, _ := authMap["token"].(string)
    if token == "" { conn.Emit("error", "missing token"); conn.Disconnect(true); return }
    // validateUserJWT(token) ...

    // Emit auth_success on success
    conn.Emit("auth_success", map[string]any{"user_id": "...", "expires_at": time.Now()})

    // Device request handler (simplified): forward to gateway after enrichment
    conn.On("device_request", func(datas ...interface{}) {
        // parse envelope (protobuf []byte or JSON), check permissions, enrich with SessionId/User/Org
        // route to specific gateway socket via gateway namespace
    })
})

// Gateway Authentication Namespace: /auth/gateway
server.Of("/auth/gateway", nil).On("connection", func(clients ...interface{}) {
    conn := clients[0].(*socket.Socket)

    // Extract and validate machine_key/api_key (DB lookups omitted here)
    authMap, _ := conn.Handshake().Auth.(map[string]interface{})
    mk, _ := authMap["machine_key"].(string)
    ak, _ := authMap["api_key"].(string)
    if mk == "" || ak == "" { conn.Emit("error", "missing machine_key or api_key"); conn.Disconnect(true); return }
    // validateGatewayAuth(mk, ak) ...

    // Emit gateway_init on success
    conn.Emit("gateway_init", map[string]string{"status": "authenticated"})

    // Device message handler (simplified): route responses back to originating FSA by SessionId
    conn.On("device_message", func(datas ...interface{}) {
        // parse envelope, set Origin=GATEWAY, emit to FSA namespace room(SessionId)
    })
})
```

### 2. Handle Device Communication Events

```go
// Handle device requests from FSA clients
server.OnEvent("/auth/fsa", "device_request", func(conn socketio.Conn, envelope *SocketEnvelope) {
  if !rh.CheckPermission(conn, envelope.DeviceId, envelope.Type) {
    conn.Emit("error", "unauthorized")
    return
  }

  // Get gateway socket ID for direct messaging
  gatewaySocketID := rh.socketRegistry.GetGatewayForDevice(envelope.DeviceId)
  if gatewaySocketID == "" {
    conn.Emit("error", "gateway not available")
    return
  }

  // Enrich envelope with session ID, user ID, and org ID for response routing
  envelope.SessionId = string(conn.ID())
  envelope.UserId = rh.GetUserID(conn)
  envelope.OrganizationId = rh.GetOrgID(conn)
  
  // Direct socket messaging to specific gateway
  server.To(socketio.Room(gatewaySocketID)).Emit("device_request", envelope)
})

// Handle device responses from Gateway clients
server.OnEvent("/auth/gateway", "device_message", func(conn socketio.Conn, envelope *SocketEnvelope) {
  // Route response back to FSA using SessionId for 1:1 delivery
  if envelope.SessionId != "" {
    server.To(socketio.Room(envelope.SessionId)).Emit("device_message", envelope)
  }
})
```

### 3. Stream Viewer Management

```go
// Simplified example: actual implementation uses zishang Socket.IO namespaces and a Redis-backed tracker.
// FSA/Onramp join/leave device stream rooms; RH counts viewers per stream type and instructs gateways via stream_control.

// Join handler (namespace: /auth/fsa)
conn.On("join", func(datas ...interface{}) {
    room := datas[0].(string) // e.g., org:<org_id>:device:<device_id>:stream_display

    // Validate permissions, parse deviceID/streamType from room (omitted here)
    // Track viewer and determine if this is the first viewer for this stream type
    firstViewer := streamTracker.AddViewer(deviceID+":"+streamType, string(conn.Id()))

    // Join the Socket.IO room for broadcasting
    conn.Join(socket.Room(room))

    // If first viewer, instruct the gateway to start this stream type
    if firstViewer {
        gatewayNamespace.To(socket.Room(gatewaySocketID)).Emit("stream_control", map[string]any{
            "action":      "start_stream",
            "device_id":   deviceID,
            "stream_type": streamType, // "stream_display" or "stream_rms"
        })
    }
})

// Leave handler (namespace: /auth/fsa)
conn.On("leave", func(datas ...interface{}) {
    room := datas[0].(string)

    // Parse deviceID/streamType, update viewer counts (omitted here)
    lastViewer := streamTracker.RemoveViewer(deviceID+":"+streamType, string(conn.Id()))

    // Leave the broadcast room
    conn.Leave(socket.Room(room))

    // If last viewer, instruct the gateway to stop this stream type
    if lastViewer {
        gatewayNamespace.To(socket.Room(gatewaySocketID)).Emit("stream_control", map[string]any{
            "action":      "end_stream",
            "device_id":   deviceID,
            "stream_type": streamType,
        })
    }
})

// Gateways do not join rooms. They send streaming frames to RH via device_message.
// RH infers the stream type from the WrapperResponse oneof and broadcasts to the appropriate stream room.
```

---

## Redis Adapter Setup

```go
// Simplified example: shows how RH creates a Socket.IO server with Redis adapter.
config := socket.DefaultServerOptions()
config.SetAdapter(&adapter.RedisAdapterBuilder{
    Redis: types.NewRedisClient(ctx, redis.NewClient(&redis.Options{Addr: os.Getenv("RH_REDIS")})),
    Opts:  &adapter.RedisAdapterOptions{},
})
server := socket.NewServer(nil, config)
```

---

## Protobuf Dispatching

```go
// Simplified example: decode inner payload based on EnvelopeType; error handling omitted.
switch env.Type {
case rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_RESPONSE:
    var wr monfwrappers.WrapperResponse
    _ = proto.Unmarshal(env.Payload, &wr)
    // use wr.GetResponse() oneof to handle specific realtime types
case rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_COMMAND:
    var wc monfwrappers.WrapperCommand
    _ = proto.Unmarshal(env.Payload, &wc)
case rushhourv1.EnvelopeType_ENVELOPE_COMMAND_JSON:
    // env.Payload contains UTF-8 JSON bytes for custom scenarios
}
```

---

## StreamTracker Example

```go
// Simplified conceptual example: actual implementation uses a Redis-backed tracker in production.
type streamKey struct {
  deviceID   string
  streamType string
}

type StreamTracker struct {
  viewers map[streamKey]map[string]struct{} // streamKey -> connIDs
  mu      sync.Mutex
}

func (st *StreamTracker) AddViewer(deviceID, streamType, connID string) bool {
  st.mu.Lock()
  defer st.mu.Unlock()
  key := streamKey{deviceID, streamType}
  if st.viewers[key] == nil {
    st.viewers[key] = make(map[string]struct{})
  }
  st.viewers[key][connID] = struct{}{}
  return len(st.viewers[key]) == 1
}

func (st *StreamTracker) RemoveViewer(deviceID, streamType, connID string) bool {
  st.mu.Lock()
  defer st.mu.Unlock()
  key := streamKey{deviceID, streamType}
  delete(st.viewers[key], connID)
  return len(st.viewers[key]) == 0
}

type StreamInfo struct {
  DeviceID   string
  StreamType string
  OrgID      string
}

func (st *StreamTracker) GetStreamsFor(connID string) []StreamInfo {
  st.mu.Lock()
  defer st.mu.Unlock()
  var result []StreamInfo
  for key, viewers := range st.viewers {
    if _, ok := viewers[connID]; ok {
      result = append(result, StreamInfo{
        DeviceID:   key.deviceID,
        StreamType: key.streamType,
      })
    }
  }
  return result
}
```

---

## Working Integration Patterns Summary

### Complete End-to-End Flow (Based on Tested Implementation)

1. **FSA Authentication**: JWT token in auth object -> `auth_success` event
2. **Gateway Authentication**: machine_key/api_key in auth object -> `gateway_init` event  
3. **FSA sends device command**: `device_request` event with SocketEnvelope
4. **RushHour enriches**: Adds SessionId, UserId, OrganizationId
5. **Gateway receives**: `device_request` event with enriched envelope
6. **Gateway responds**: `device_message` event preserving SessionId
7. **FSA receives**: `device_message` event routed by SessionId

### Authentication Troubleshooting Guide

| Problem | Symptom | Solution |
|---------|---------|----------|
| Connection timeout | Client never connects | Check network connectivity and server availability |
| Auth failure | Connection but no auth event | Ensure auth data in `.auth` object, not query params |
| Wrong auth event | Connect but waiting forever | FSA expects `auth_success`, Gateway expects `gateway_init` |
| Message not routed | Commands sent but not received | Check namespace: `/auth/fsa` vs `/auth/gateway` |
| Response not returned | Gateway responds but FSA doesn't get it | Gateway must preserve `SessionId` from request |
| Test timing issues | Inconsistent test results | Ensure stable network and increase timeouts if needed |

### Transport Configuration Guidelines

**For normal application use:**
- JavaScript: `transports: ["websocket", "polling"]` (WebSocket preferred, polling fallback)
- Dart/Flutter: `.setTransports(['websocket', 'polling'])`
- Go: `opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))`

### Required Authentication Structure

**FSA (JWT):**
```json
{
  "auth": {
    "token": "jwt_token_here"
  }
}
```

**Gateway (Machine Key + API Key):**
```json
{
  "auth": {
    "machine_key": "machine_key_here",
    "api_key": "api_key_here"
  }
}
```

