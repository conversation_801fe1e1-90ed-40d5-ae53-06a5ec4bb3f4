# Messaging Summary (FSA, Gateway, RH)

This document summarizes Socket.IO namespaces, events, and field rules for each participant in the Rush Hour (RH) system.

- FSA: Field Service App clients connect on `/auth/fsa`
- Gateway: Device gateways connect on `/auth/gateway`
- RH: Rush Hour Socket.IO server (emits and routes events)

---

## Quick Rules for Envelope Fields

- device_request (FSA → RH → Gateway)
  - Required: `DeviceId`, `Type`, `Payload` (command)
  - Set by RH: `SessionId`, `UserId`, `OrganizationId`, `Origin`
  - Forbidden (client-provided): `SessionId`, `UserId`

- device_message (Gateway → RH → FSA) [1:1 response]
  - Required (from gateway): `SessionId`, `UserId`, `DeviceId`, `OrganizationId`, `Type`, `Payload`
  - Set by RH: `Origin = Gateway`
  - Validation: RH rejects if `SessionId` or `UserId` missing; rejects if `SessionId` user mismatches `UserId`

- stream_display / stream_rms (Gateway → RH → viewers)
  - Forbidden (from gateway): `SessionId`, `UserId`
  - RH enriches `Origin` and broadcasts to canonical device stream room

- Errors
  - RH emits `error` on parse/validation failures and on auth/permission denials

---

## FSA (Field Service App / Onramp)

- Namespace: `/auth/fsa`

### Inbound (received by FSA)

| Event | Namespace | From | Purpose | Required fields | Forbidden fields | Notes |
|---|---|---|---|---|---|---|
| `auth_success` | `/auth/fsa` | RH | Confirms successful authentication | `user_id`, `expires_at` | — | Sent after JWT validation |
| `device_message` | `/auth/fsa` | RH | 1:1 response or broadcast stream frame | Response: `SessionId`, `UserId`, `DeviceId`, `OrganizationId`, `Type`, `Payload` | — | For streams, delivered to the room as `device_message` (no SessionId/UserId in the inbound gateway frame) |
| `joined` | `/auth/fsa` | RH | Confirms join to canonical stream room | Canonical room string | — | Room: `org:<OrgID>:device:<DeviceID>:stream_display` or `stream_rms` |
| `left` | `/auth/fsa` | RH | Confirms leave from canonical stream room | Canonical room string | — | — |
| `error` | `/auth/fsa` | RH | Error notification | message string | — | Emitted on parse/validation/permission errors |

### Outbound (sent by FSA)

| Event | Namespace | To | Purpose | Required fields | Forbidden fields | Notes |
|---|---|---|---|---|---|---|
| `device_request` | `/auth/fsa` | RH | Send device command | `DeviceId`, `Type`, `Payload` | `SessionId`, `UserId` | RH fills `SessionId`, `UserId`, `OrganizationId`, `Origin` |
| `join` | `/auth/fsa` | RH | Request to view a device stream | Room string (may include OrgID) | — | RH validates org; joins canonical room and begins stream |
| `leave` | `/auth/fsa` | RH | Stop viewing a device stream | Room string | — | RH validates org; stops stream if last viewer |

---

## Gateway

- Namespace: `/auth/gateway`

### Inbound (received by Gateway)

| Event | Namespace | From | Purpose | Required fields | Forbidden fields | Notes |
|---|---|---|---|---|---|---|
| `gateway_init` | `/auth/gateway` | RH | Confirms successful authentication | status | — | Emitted after Machine Key + API Key auth |
| `device_request` | `/auth/gateway` | RH | Device command from FSA | `DeviceId`, `Type`, `Payload`, `SessionId`, `UserId`, `OrganizationId`, `Origin` | — | Gateway must preserve `SessionId` and `UserId` in responses |
| `stream_control` | `/auth/gateway` | RH | Start/stop stream instruction | `device_id`, `stream_type`, `action` | — | Actions: `start_stream`, `end_stream` |
| `error` | `/auth/gateway` | RH | Error notification | message string | — | Parse/validation/permission errors |

### Outbound (sent by Gateway)

| Event | Namespace | To | Purpose | Required fields | Forbidden fields | Notes |
|---|---|---|---|---|---|---|
| `device_message` | `/auth/gateway` | RH | 1:1 response to device_request | `SessionId`, `UserId`, `DeviceId`, `OrganizationId`, `Type`, `Payload`, `Origin` | — | RH routes to FSA via `SessionId` |
| `device_message` | `/auth/gateway` | RH | 1:1 response to device_request | `SessionId`, `UserId`, `DeviceId`, `OrganizationId`, `Type`, `Payload` | — | RH sets `Origin=Gateway` and routes to FSA via `SessionId` |
| `stream_display` | `/auth/gateway` | RH | Broadcast display frame | `DeviceId`, `OrganizationId`, `Type`, `Payload`, `Origin` | `SessionId`, `UserId` | RH rebroadcasts to room as `device_message` |
| `stream_rms` | `/auth/gateway` | RH | Broadcast RMS frame | `DeviceId`, `OrganizationId`, `Type`, `Payload`, `Origin` | `SessionId`, `UserId` | RH rebroadcasts to room as `device_message` |

---

## Rush Hour (RH Server)

- Namespaces handled: `/auth/fsa`, `/auth/gateway`, `/`

### Inbound (received by RH)

| Event | Namespace | From | Purpose | Validation |
|---|---|---|---|---|
| `device_request` | `/auth/fsa` | FSA/Onramp | Forward device command to responsible gateway | Rejects if `SessionId`/`UserId` present; fills them; org/device permissions enforced |
| `device_message` | `/auth/gateway` | Gateway | 1:1 response routing back to FSA | Requires `SessionId` and `UserId`; validates session-user match |
| `stream_display` | `/auth/gateway` | Gateway | Broadcast to stream room | Rejects if `SessionId`/`UserId` present |
| `stream_rms` | `/auth/gateway` | Gateway | Broadcast to stream room | Rejects if `SessionId`/`UserId` present |
| `join` | `/auth/fsa` | FSA/Onramp | Request to join device stream | Validates org, permissions; canonicalizes room; triggers `start_stream` if first viewer |
| `leave` | `/auth/fsa` | FSA/Onramp | Request to leave device stream | Stops stream via `end_stream` if last viewer |

### Outbound (sent by RH)

| Event | Namespace | To | Purpose | Notes |
|---|---|---|---|---|
| `auth_success` | `/auth/fsa` | FSA | Confirms FSA authentication | Includes `user_id`, `expires_at` |
| `gateway_init` | `/auth/gateway` | Gateway | Confirms gateway authentication | — |
| `device_request` | `/auth/gateway` | Gateway | Forwards device command | Includes `SessionId`, `UserId`, `OrganizationId`, `Origin` |
| `device_message` | `/auth/fsa` | FSA | 1:1 response or stream frame | For streams, broadcast to viewers in canonical room |
| `stream_control` | `/auth/gateway` | Gateway | Start/stop stream | Emitted on first/last viewer joins/leaves |
| `joined` | `/auth/fsa` | FSA | Confirms join to canonical room | Canonical room string returned |
| `left` | `/auth/fsa` | FSA | Confirms leave from canonical room | Canonical room string returned |
| `error` | `/auth/fsa`, `/auth/gateway` | FSA/Gateway | Error notification | Emitted on parse/validation/permission/auth failures |

---

## Streaming Rooms and Canonical Routing

- Canonical device stream room format: `org:<OrgID>:device:<DeviceID>:stream_display|stream_rms`
- Only RH emits to stream rooms. Clients request membership via `join`/`leave`; RH manages viewer counts and emits `stream_control` to gateways accordingly.

---

## Field Semantics Reference

| Field | device_request (FSA→RH) | device_request (RH→Gateway) | device_message (Gateway→RH) | device_message (RH→FSA) | stream_* (Gateway→RH) | stream_* (RH→FSA room) |
|---|---|---|---|---|---|---|
| `SessionId` | Forbidden | Required (set by RH) | Required | Present (for 1:1), absent for broadcasts | Forbidden | Absent in inbound; broadcast does not target a session |
| `UserId` | Forbidden | Required (set by RH) | Required | Present (for 1:1), absent for broadcasts | Forbidden | Absent in inbound; broadcast does not target a session |
| `OrganizationId` | Optional (validated vs device) | Required (set by RH) | Required | Present | Required | Present |
| `Origin` | FSA/Onramp (set by RH) | FSA/Onramp | Set by RH to Gateway | Gateway | Set by RH to Gateway | Gateway |

---

## Errors

- RH emits `error` to clients when:
  - Auth fails (missing/invalid credentials)
  - Envelope parse fails
  - Forbidden fields are present (`SessionId`/`UserId` where not allowed)
  - Required fields are missing or inconsistent (e.g., missing `SessionId`,`UserId` on `device_message`)
  - Permission checks fail (device/room access)

This summary reflects the current, authoritative behavior implemented in RH and validated by tests.
