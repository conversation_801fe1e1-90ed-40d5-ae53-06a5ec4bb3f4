# Streaming Architecture - Device Room Model

## Overview

rushhour implements a unified streaming model where gateways, FSA clients, and Onramp clients participate with different roles and behaviors:

- **Gateways**: Producers only (send streaming data to RH via `device_message`; do not join rooms)
- **FSA Clients**: Field technicians (receive device data, send device commands)
- **Onramp Clients**: Technical support (receive device data, send device commands with potentially greater permissions) - authentication pending; see architecture doc note
- **Viewer Counting**: Only FSA and Onramp clients count toward viewership totals

## Room Structure

### Device Streaming Rooms
```
Pattern: org:<org_id>:device:<device_id>:<stream_type>

Examples: 
  org:123:device:abc456:stream_display  // Visual/display data stream
  org:123:device:abc456:stream_rms      // RMS measurement data stream
```

**Stream Types:**
- `stream_display`: Visual display data for device monitoring interfaces
- `stream_rms`: RMS (Root Mean Square) measurement data for analytical purposes

### Gateway Control (Direct Socket Messaging)

RushHour uses direct socket messaging to control gateway streaming production. RH emits `stream_control` to the specific gateway socket in the `/auth/gateway` namespace. Gateways do not join Socket.IO rooms; they stream to RH, and RH distributes to stream rooms.

Control messages:
```json
{
  "action": "start_stream" | "end_stream",
  "device_id": "abc456",
  "stream_type": "stream_display" | "stream_rms"
}
```

## Streaming Flow

### 1. FSA/Onramp Client Joins Device Stream Room
```typescript
// FSA or Onramp client wants to watch device display data
socket.emit("join", "org:123:device:abc456:stream_display")

// Or watch RMS measurement data
socket.emit("join", "org:123:device:abc456:stream_rms")
```

**Server Response:**
1. Validates FSA/Onramp client permissions for specific stream type
2. Adds FSA/Onramp client to device room
3. Tracks as first viewer for this stream type (if applicable)
4. If first viewer -> signals gateway to start streaming this type

### 2. Gateway Receives Stream Start Command (Direct)
RH sends `stream_control` directly to the gateway socket ID:
```json
{
  "action": "start_stream",
  "device_id": "abc456",
  "stream_type": "stream_display"
}
```

**Gateway Response (current design):**
1. Begins producing streaming data for the requested stream type
2. Sends frames to RH via `device_message` (no room join)
3. RH infers stream type and broadcasts to the appropriate device stream room

### 3. Data Publishing
```go
// Gateways emit stream-specific events for broadcast streaming
respEnv := &rushhourv1.SocketEnvelope{
    Type:           rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_RESPONSE,
    RequestId:      reqId,
    SessionId:      "", // broadcast path (ignored by RH for stream events)
    DeviceId:       deviceID,
    OrganizationId: orgID,
    Origin:         rushhourv1.OriginType_ORIGIN_GATEWAY,
    Payload:        wrapperResponseBytes,
}
envBytes, _ := proto.Marshal(respEnv)
client.Emit("stream_display", envBytes) // or client.Emit("stream_rms", envBytes)
```

**Behavior:**
- FSA and Onramp clients receive the data
- Gateway does NOT receive its own messages and is not a room member

### 4. FSA/Onramp Client Leaves Device Stream Room
```typescript
// FSA or Onramp client stops watching display stream
socket.emit("leave", "org:123:device:abc456:stream_display")
```

**Server Response:**
1. Removes FSA/Onramp client from room
2. Checks if last viewer for this stream type
3. If last viewer -> signals gateway to stop this stream type

### 5. Gateway Receives Stream Stop Command (Direct)
RH sends `stream_control` directly to the gateway socket ID:
```json
{
  "action": "end_stream",
  "device_id": "abc456",
  "stream_type": "stream_display"
}
```

**Gateway Response (current design):**
1. Stops producing streaming data for this stream type (no room leave)

## Independent Stream Management

**Important:** Each stream type is managed independently:

- A device can have `stream_display` active while `stream_rms` is inactive
- Different FSA/Onramp clients can subscribe to different stream types
- Gateways do not join rooms; RH broadcasts to rooms after receiving device_message
- Viewer counts are tracked separately per stream type

**Example Scenario:**
<!-- NON-ASCII INTENTIONAL: box-drawing used to clearly depict hierarchy -->
```
Device ABC456 state:
├─ stream_display: 3 FSA/Onramp viewers -> Gateway producing, RH broadcasting
└─ stream_rms: 0 FSA/Onramp viewers -> Gateway not producing, no broadcast
```

## Key Benefits

### Granular Stream Control
- Independent control of display vs. RMS data streams
- Efficient bandwidth usage (only stream what's being watched)
- Separate permission control per stream type

### Accurate Per-Type Viewer Counting
- Only FSA and Onramp clients counted as viewers
- Gateway presence doesn't affect viewer counts
- Stream start/stop based on actual viewership per type

### Efficient Resource Usage
- Streaming only when FSA/Onramp clients are watching specific stream types
- Automatic cleanup when no viewers for a stream type
 

### Clear Role Separation
- **Gateways**: Data producers (send device_message to RH; do not join rooms)
- **FSA Clients**: Field technicians (join rooms, receive data, send device commands)
- **Onramp Clients**: Technical support (join rooms, receive data, send device commands with potentially greater permissions)

## Implementation Details

### Stream Key Format
```go
// Viewer tracking uses composite keys for stream type separation
streamKey := fmt.Sprintf("%s:%s", deviceID, streamType)
// Examples: "device123:stream_display", "device123:stream_rms"
```

### Viewer Tracking
```go
// Only FSA and Onramp clients are counted as viewers for specific stream types
if ctx.AuthInfo == nil || 
   (ctx.AuthInfo.ClientType != domain.ClientTypeFSA && ctx.AuthInfo.ClientType != domain.ClientTypeOnramp) {
    return fmt.Errorf("only FSA and Onramp clients can join device stream channels")
}

// Track viewers per stream type
isFirstViewer := streamTracker.AddViewer(streamKey, connID)
```

### RH Broadcasting
```go
// RH receives stream_display/stream_rms and broadcasts to the device stream room
deviceRoom := GetDeviceStreamChannel(orgID, deviceID, streamType)
server.To(Room(deviceRoom)).Emit("device_message", envelope)
```

### Room Membership Examples
- **Display Stream Room**: `org:123:device:abc456:stream_display`
  - Members: FSA/Onramp clients (subscribers)
- **RMS Stream Room**: `org:123:device:abc456:stream_rms`
  - Members: FSA/Onramp clients (subscribers)
// Gateway control uses direct socket `stream_control` to the gateway socket; gateway is not a room member.

## Stream Type Permissions

Different stream types can have different permission requirements:

```go
// Permission checking per stream type
streamAction := "stream_display"  // or "stream_rms"
if err := permissionChecker.CheckDeviceAccess(ctx, deviceID, streamAction); err != nil {
    return fmt.Errorf("access denied to device %s stream type %s", deviceID, streamType)
}
```

## Command Permissions (Future Implementation)

**Current Status: POC Phase**
- Both FSA and Onramp clients can send device commands
- Authorization currently relies on JWT/Token auth and basic device access permissions
- Onramp users (technical support) may have greater permissions than field technicians

**Planned Implementation:**
- Granular command-level permissions based on user roles
- Different permission levels for different command types:
  - Read-only commands (device status, configuration viewing)
  - Control commands (parameter changes, operational controls)  
  - Administrative commands (firmware updates, factory resets)
- Role-based access control (RBAC) with command-specific permissions

```go
// Future granular permission checking (not yet implemented)
switch envelope.CommandType {
case "device_status", "read_config":
    // Basic device access required
case "set_parameters", "control_operation":
    // Device control permissions required  
case "firmware_update", "factory_reset":
    // Administrative permissions required
}
```

This architecture provides granular stream control while maintaining clean separation of concerns and efficient resource usage. 