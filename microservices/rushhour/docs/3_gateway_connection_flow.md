# Gateway Connection Flow (Rush Hour)

This document outlines the connection and authorization flow for **Gateway** clients connecting to the Rush Hour (RH) Socket.IO server. The architecture uses **direct socket messaging** for all device communication.

**Development and Testing Guide** - All code examples are based on tested, working implementations validated through end-to-end testing and ready for integration development.

---

## Overview

- Gateway authenticates using an existing **token-based REST API flow**, receiving a long-lived **API key**
- Gateway connects to RH via the Socket.IO namespace: `/auth/gateway`
- R<PERSON> validates the API key and registers the gateway for direct socket messaging
- RH responds with `gateway_init` (confirming authentication)

---

## 1. Gateway Authentication Requirements

**Critical: Gateway authentication has been a common source of integration issues. Follow these requirements:**

### Correct Gateway Client Implementation (Go)

```go
import (
    "github.com/zishang520/engine.io-client-go/transports"
    "github.com/zishang520/engine.io/v2/types"
    "github.com/zishang520/socket.io-client-go/socket"
)

// Create connection options
opts := socket.DefaultOptions()
// WebSocket preferred, polling fallback
opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))

// REQUIRED: Auth data in the auth object (not query parameters)
authData := map[string]interface{}{
    "machine_key": "your_machine_key",
    "api_key":     "your_api_key",
}
opts.SetAuth(authData)

// Create manager and connect to gateway namespace
manager := socket.NewManager("https://<rushhour domain name>", opts)
client := manager.Socket("/auth/gateway", opts)

// REQUIRED: Listen for gateway_init event (not connect)
client.On("gateway_init", func(args ...any) {
    log.Printf("Gateway authenticated successfully: %v", args)
    // Now ready for device communication
})

client.On("connect", func(args ...any) {
    log.Print("Connected to Socket.IO")
})

client.On("connect_error", func(args ...any) {
    log.Printf("Connection error: %v", args)
})

// Handle device requests from RH (originating from FSA)
client.On("device_request", func(args ...any) {
    envelope := parseSocketEnvelope(args[0])
    log.Printf("Received device command - Device: %s, Session: %s", 
               envelope.DeviceId, envelope.SessionId)
    
    // Process device command and send response
    processDeviceCommand(client, envelope)
})

// Start connection
client.Connect()
```

### Common Gateway Authentication Mistakes

**DO NOT use query parameters:**
```go
// WRONG: Auth data in URL query parameters
client, err := socketio_client.NewClient("https://<rushhour domain name>/auth/gateway?machine_key=xyz&api_key=abc", opts)
```

**DO NOT rely on connect event for auth:**
```go
// WRONG: connect event fires before authentication
client.On("connect", func(args ...any) {
    // Authentication not guaranteed here!
})
```

**Transport Configuration Notes:**
```go
// WebSocket with polling fallback (recommended)
opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))
```

---

## 2. RH Authentication Handler (Server Side - Go)

```go
// Simplified pseudocode using zishang Socket.IO server (matches RushHour implementation)
gatewayNS := server.Of("/auth/gateway", nil)
gatewayNS.On("connection", func(clients ...interface{}) {
    conn := clients[0].(*socket.Socket)

    // Extract auth from handshake (auth map provided by client options)
    authMap, _ := conn.Handshake().Auth.(map[string]interface{})
    machineKey, _ := authMap["machine_key"].(string)
    apiKey, _ := authMap["api_key"].(string)
    if machineKey == "" || apiKey == "" {
        conn.Emit("error", "missing machine_key or api_key")
        conn.Disconnect(true)
        return
    }

    // Validate credentials and register context (DB lookups omitted)
    // service.gatewayAuth.HandleGatewayAuth(conn, machineKey, apiKey, service)

    // On success, confirm authentication
    conn.Emit("gateway_init", map[string]string{"status": "authenticated"})

    // Set up gateway-side inbound handlers (simplified)
    // 1:1 responses from gateways back to RH (SessionId required)
    conn.On("device_message", func(datas ...interface{}) {
        // Parse protobuf SocketEnvelope and route back to the originating FSA session
    })

    // Broadcast streaming from gateways (no SessionId allowed)
    conn.On("stream_display", func(datas ...interface{}) {
        // Parse protobuf SocketEnvelope and broadcast to device stream_display room
    })

    conn.On("stream_rms", func(datas ...interface{}) {
        // Parse protobuf SocketEnvelope and broadcast to device stream_rms room
    })
})
```

---

## 3. Device Communication Examples

### Processing Device Commands and Sending Responses

```go
// Simplified protobuf example: gateway responds with WrapperResponse inside SocketEnvelope (binary)
func processDeviceCommand(client *socket.Socket, envelope *SocketEnvelope) {
    // Unmarshal WrapperCommand from envelope.Payload (protobuf)
    var wc monfwrappers.WrapperCommand
    if err := proto.Unmarshal(envelope.Payload, &wc); err != nil {
        return
    }

    // Build a WrapperResponse (protobuf)
    wr := &monfwrappers.WrapperResponse{
        Version:   1,
        RequestId: envelope.RequestId,
        Code:      monfwrappers.EResponseCodes_RESP_SUCCESS,
        // Set an appropriate oneof payload here
    }
    wrBytes, _ := proto.Marshal(wr)

    // Wrap in SocketEnvelope and emit to RH (SessionId and UserId must be preserved)
    resp := &SocketEnvelope{
        Type:           ENVELOPE_WRAPPER_RESPONSE,
        RequestId:      envelope.RequestId,
        SessionId:      envelope.SessionId,
        UserId:         envelope.UserId,
        DeviceId:       envelope.DeviceId,
        OrganizationId: envelope.OrganizationId,
        Origin:         ORIGIN_GATEWAY,
        Payload:        wrBytes,
    }
    respBytes, _ := proto.Marshal(resp)
    client.Emit("device_message", respBytes)
}
```

### Message Envelope Structure

All device communication uses the `SocketEnvelope` protobuf (outer envelope) serialized to bytes. The inner payload format depends on `EnvelopeType`.

```proto
message SocketEnvelope {
  EnvelopeType type = 1;         // Determines how to interpret payload
  uint32 request_id = 2;         // For request/response correlation
  string session_id = 3;         // RH populates for routing; gateway must preserve in 1:1 responses, omit for streaming responses
  string user_id = 4;            // RH populates from FSA/Onramp auth
  string device_id = 5;          // Target device ID
  string organization_id = 6;    // RH populates from auth/device org
  OriginType origin = 7;         // ORIGIN_FSA, ORIGIN_GATEWAY, etc.
  bytes payload = 8;             // Protobuf or JSON bytes, depending on type
}
```

Clarifications for gateway responses:

- type: use ENVELOPE_WRAPPER_RESPONSE when returning a device response that carries a WrapperResponse payload.
- origin: set to ORIGIN_GATEWAY when the response originates from the gateway.
- session_id: copy from the incoming command for 1:1 responses; leave empty for streaming/broadcast messages.
- user_id: copy from the incoming command for 1:1 responses; leave empty for streaming/broadcast responses. RH requires UserId on 1:1 responses and rejects UserId on streaming messages.
- payload: normally protobuf-serialized WrapperResponse bytes. For ENVELOPE_COMMAND_JSON, payload contains UTF-8 JSON bytes (less common).

### SessionId and UserId Semantics (Gateway)

- Gateways must not set `SessionId` on incoming commands; RH provides it. Gateways must copy the `SessionId` from the command envelope into the response envelope so RH can deliver the response back to the originating FSA/Onramp.
- Gateways must copy `UserId` from the command envelope into 1:1 responses; RH uses it for validation and routing. Do not include `UserId` on streaming messages.
- For broadcast streaming messages (no specific requester), omit `SessionId` and `UserId`; RH will broadcast to the relevant device stream rooms.

### Choosing Transport

- Always send the outer `SocketEnvelope` as protobuf-serialized bytes.
- The inner `payload` may be JSON when `EnvelopeType` indicates JSON (e.g., `ENVELOPE_COMMAND_JSON`); otherwise it is protobuf for known types.

---

## 4. Direct Messaging and Streaming Communication

With direct socket messaging, gateways can:

- **Receive device commands**: RH routes device requests directly to the specific gateway socket
- **Send device responses (1:1)**: Gateway sends responses using `device_message` and MUST include `SessionId`; RH routes back to the originating FSA/Onramp socket
- **Stream broadcast data**: Gateway emits `stream_display` or `stream_rms` to RH for broadcasting; RH routes to the appropriate stream room(s)
- **Start/Stop streaming**: Gateway begins/ends streaming when instructed via `stream_control` based on viewer presence

Example device message handling:

```go
server.OnEvent("/auth/gateway", "device_message", func(conn socketio.Conn, envelope SocketEnvelope) {
    // 1:1 device response from gateway (SessionId required)
    service.ProcessDeviceMessage(conn, &envelope)
})
server.OnEvent("/auth/gateway", "stream_display", func(conn socketio.Conn, envelope SocketEnvelope) {
    service.ProcessGatewayStream(conn, &envelope, "stream_display")
})
server.OnEvent("/auth/gateway", "stream_rms", func(conn socketio.Conn, envelope SocketEnvelope) {
    service.ProcessGatewayStream(conn, &envelope, "stream_rms")
})
// Note: See the Streaming Architecture document for information about stream start/stop messages.
```

---

## 4. API Key Validation (Current Implementation)

For informational purposes, the RH microservice authenticates the gateway connection in this way:

```go
// Simplified excerpt based on RushHour implementation (auth/gateway.go)
// Uses shared authorizer to validate machine_key + api_key and return gateway/org IDs.

// Dependency (injected for testing)
var validateGatewayAuthFunc = authorizer.ValidateGatewayAuth

type GatewayAuthenticator struct {
    db connect.DatabaseExecutor
}

func (ga *GatewayAuthenticator) ValidateGatewayAuth(machineKey, apiKey string) (*domain.AuthInfo, error) {
    // Validate credentials using shared authorizer (DB-backed)
    gatewayInfo, err := validateGatewayAuthFunc(ga.db, machineKey, apiKey)
    if err != nil {
        return nil, fmt.Errorf("invalid gateway credentials")
    }

    // Map to domain model used by RushHour
    return &domain.AuthInfo{
        ClientType: domain.ClientTypeGateway,
        GatewayID:  gatewayInfo.GatewayID,
        OrgID:      gatewayInfo.OrgID,
    }, nil
}
```

---

This flow securely initializes the gateway and registers it for direct socket messaging. In RH, device communication is routed via socket IDs.

---

## Gateway Authentication Quick Reference

### Working Pattern
1. Machine key + API key credentials
2. WebSocket with polling fallback
3. Auth data in `.auth` object with `"machine_key"` and `"api_key"` keys
4. Connect to `/auth/gateway` namespace
5. Wait for `gateway_init` event (not `connect`)
6. Listen for `device_request` from RH (originating from FSA)
7. Use `device_message` to send responses (preserve `SessionId` and `UserId` for 1:1 responses)

### Common Issues
- **Query parameters**: Server expects auth object
- **Wrong namespace**: Must use `/auth/gateway`
- **Wrong event**: Wait for `gateway_init`, not `connect`
- **Missing SessionId/UserId**: Must preserve `SessionId` and `UserId` from the request in 1:1 responses
- **Streaming fields present**: Do not include `SessionId` or `UserId` on `stream_display` or `stream_rms` events


### Test Credentials (DEV environment)
```
Machine Key: localgateway
API Key: qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd
Device ID: 87d94e14-e804-58b3-9f8c-a02e5de90aeb
```

