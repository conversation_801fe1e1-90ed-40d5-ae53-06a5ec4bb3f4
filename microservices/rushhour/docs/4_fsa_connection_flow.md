# Field Service App (FSA) Connection Flow - Rush Hour

This document outlines the connection and authorization flow for the **Field Service App (FSA)**, a Flutter/Dart-based client, connecting to the Rush Hour (RH) Socket.IO server.

**Development and Testing Guide** - All code examples are based on tested, working implementations validated through end-to-end testing and ready for integration development (in the Go language).

---

## Overview

- The FSA authenticates using a **JWT** (JSON Web Token) via an existing identity provider
- The FSA connects to RH via the Socket.IO namespace: `/auth/fsa`
- RH validates the JWT and determines the user's `user_id` and the list of organizations they belong to
- RH registers the FSA socket for direct messaging capability
- The FSA can request to join device streaming rooms and send device commands via direct messaging

---

## 1. FSA Authentication Requirements

**Critical: FSA authentication has been a common source of integration issues. Follow these requirements:**

**NOTE: This is an opinionated document from an AI that has not actually tested the Dart code.  The overall concepts are fine, but use your experience, too.**

### Correct FSA Client Implementation (Dart/Flutter)

```dart
import 'package:socket_io_client/socket_io_client.dart' as IO;

final socket = IO.io(
  'https://<rushhour domain name>/auth/fsa',  // Must use /auth/fsa namespace
  IO.OptionBuilder()
    .setTransports(['websocket', 'polling'])  // WebSocket preferred, polling fallback
    .setAuth({ 'token': jwt })   // REQUIRED: JWT in auth object with 'token' key
    .build(),
);

socket.onConnect((_) {
  print('Connected to Socket.IO');
});

// REQUIRED: Listen for auth_success event (not connect)
socket.on('auth_success', (data) {
  print('FSA authenticated successfully');
  print('user_id: ${data['user_id']}');
  print('expires_at: ${data['expires_at']}');
  // Now ready for device commands and streaming
});

socket.on('connect_error', (error) {
  print('Connection error: $error');
});

socket.on('error', (msg) {
  print('Auth error: $msg');
});

// Handle device responses
socket.on('device_message', (envelope) {
  print('Received device response: $envelope');
  // Process device response from gateway
});
```

### Common FSA Authentication Mistakes

**DO NOT use query parameters:**
```dart
// WRONG: Auth data in URL query parameters
final socket = IO.io('https://<rushhour domain name>/auth/fsa?token=$jwt');
```

**DO NOT rely on connect event for auth:**
```dart
// WRONG: connect event fires before authentication
socket.onConnect((_) {
  // Authentication not guaranteed here!
});
```

**Transport Configuration Notes:**
```dart
// WebSocket with polling fallback (recommended)
.setTransports(['websocket', 'polling'])
```

### Empty Token Behavior (clarification)

- The server expects the `auth` object to include a `token` key. If the key is present but the token is empty or invalid, authentication will fail and the server will emit an `error` of `"unauthorized"` and then disconnect the socket.
- The Socket.IO `connect` event may fire before authentication completes. Do not treat `connect` as success; wait for the `auth_success` event to confirm authentication.
- Recommended client behavior: always supply a non-empty JWT and rely solely on `auth_success` to proceed with device commands or streaming.

### SessionId Semantics (FSA)

- Do not set `SessionId` in outgoing envelopes from the FSA. RushHour will set/overwrite the `SessionId` to the calling socket's session ID for routing of responses.
- The `SessionId` is an internal routing value used by RH and must be preserved by the gateway in its response envelope.
- For streaming/viewing scenarios, `SessionId` is not used; join the appropriate stream room(s). Gateways emit `stream_display` / `stream_rms` events to RH for broadcasts.

### RH Envelope Enrichment and Organization Validation

- On incoming device commands, RH enriches the `SocketEnvelope` with routing metadata:
  - Sets `SessionId` to the caller's Socket.IO session ID (used to route responses back 1:1).
  - Sets `UserId` based on the authenticated FSA user.
  - Sets `OrganizationId` strictly to the device's organization (fetched from the database).
- RH validates organization consistency before routing the command:
  - If the envelope contains a non-empty `OrganizationId` and it does not match the device's organization, RH rejects the command with an organization mismatch error.
  - Otherwise, RH overwrites `OrganizationId` with the device's organization for consistent downstream processing.

### Choosing Transport

- Always send the outer `SocketEnvelope` as protobuf-serialized bytes for production performance and efficiency.
- The inner `payload` can be either protobuf or JSON, based on `EnvelopeType` (e.g., `ENVELOPE_COMMAND_JSON` for JSON payloads).

---

## 2. RH Authentication Handler (Server Side - Go)

And example of Rush Hour's perspective:

```go
// Simplified pseudocode using zishang Socket.IO server (matches RushHour implementation)
fsaNS := server.Of("/auth/fsa", nil)
fsaNS.On("connection", func(clients ...interface{}) {
    conn := clients[0].(*socket.Socket)

    // Extract token from handshake auth map
    authMap, _ := conn.Handshake().Auth.(map[string]interface{})
    token, _ := authMap["token"].(string)
    if token == "" {
        conn.Emit("error", "missing token")
        conn.Disconnect(true)
        return
    }

    // Validate JWT and register connection context (DB calls omitted)
    // service.fsaAuth.HandleFSAAuth(conn, authMap, db, service)

    // On success, emit auth_success
    conn.Emit("auth_success", map[string]interface{}{
        "user_id":    "...",
        "expires_at": time.Now().UTC().Format(time.RFC3339),
    })
})
```

---

## 3. Token Validation (Current Implementation)

```go
// Simplified excerpt based on RushHour implementation (auth/fsa.go)
// Uses shared authorizer to validate the JWT and load permissions/orgs.

// Dependency (injected for testing)
var validateJWTAndGetPermissionsFunc = authorizer.ValidateJWTAndGetPermissions

type FSAAuthenticator struct {
    db connect.DatabaseExecutor
}

func (fa *FSAAuthenticator) ValidateUserJWT(token string, pg connect.DatabaseExecutor) (*domain.AuthInfo, error) {
    if token == "" {
        return nil, fmt.Errorf("empty token")
    }

    // Validate JWT and load permissions from DB
    perms, err := validateJWTAndGetPermissionsFunc(pg, token)
    if err != nil {
        return nil, fmt.Errorf("authentication failed: %w", err)
    }

    var orgID string
    if len(perms.Permissions) > 0 {
        orgID = perms.Permissions[0].OrganizationID
    }

    return &domain.AuthInfo{
        ClientType:  domain.ClientTypeFSA,
        UserID:      perms.UserID,
        OrgID:       orgID,
        Permissions: perms,
        // Placeholder expiration; RH includes expires_at in auth_success for clients
        ExpiresAt:   time.Now().Add(24 * time.Hour),
    }, nil
}
```

---

This flow securely authenticates the FSA app, registers it for direct socket messaging, and returns user identity information. The FSA can then send device commands via direct messaging and join device streaming rooms.

---

## FSA Authentication Quick Reference

### Working Pattern
1. JWT from broker authentication
2. WebSocket with polling fallback
3. Auth data in `.auth` object with `"token"` key
4. Connect to `/auth/fsa` namespace
5. Wait for `auth_success` event (not `connect`)
6. Use `device_request` to send commands
7. Listen for `device_message` for responses

### Common Issues
- **Query parameters**: Server expects auth object
- **Wrong namespace**: Must use `/auth/fsa`
- **Wrong event**: Wait for `auth_success`, not `connect`


### Test Credentials (DEV environment)
```
Username: <EMAIL>
Password: puppies1234
Device ID: 87d94e14-e804-58b3-9f8c-a02e5de90aeb
```

