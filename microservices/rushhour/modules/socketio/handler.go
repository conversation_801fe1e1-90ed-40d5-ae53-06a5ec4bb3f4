package socketio

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/zishang520/socket.io/v2/socket"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/rushhour/domain"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// <PERSON><PERSON> manages Socket.io event handling and client connections
type Handler struct {
	server  *socket.Server
	service *Service
	db      *connect.DatabaseExecutor
}

// NewHandler creates a new Socket.io handler
func NewHandler(server *socket.Server, service *Service, db *connect.DatabaseExecutor) *Handler {
	return &Handler{
		server:  server,
		service: service,
		db:      db,
	}
}

// SetupHandlers initializes all Socket.io event handlers
func (h *Handler) SetupHandlers() {
	h.setupEventHandlers()
}

// setupEventHandlers configures all Socket.io event handlers
func (h *Handler) setupEventHandlers() {
	// Main namespace for basic functionality
	h.registerBasicClientEvents("/")

	// Gateway authentication namespace
	h.registerGatewayEvents(domain.NamespaceGateway)

	// FSA (Field Service App) authentication namespace
	h.registerFSAEvents(domain.NamespaceFSA)
}

// registerGatewayEvents sets up event handlers for gateway clients
// registerGatewayEvents sets up event handlers for gateway clients
func (h *Handler) registerGatewayEvents(namespace string) {
	gatewayNamespace := h.server.Of(namespace, nil)

	gatewayNamespace.On("connection", func(clients ...interface{}) {
		conn := clients[0].(*socket.Socket)
		logger.Infof("Gateway connecting: %s", conn.Id())

		authMap, ok := conn.Handshake().Auth.(map[string]interface{})
		if !ok {
			h.disconnectWithError(conn, "invalid auth data")
			return
		}

		machineKey, mkOK := authMap["machine_key"].(string)
		apiKey, akOK := authMap["api_key"].(string)

		if !mkOK || !akOK || machineKey == "" || apiKey == "" {
			h.disconnectWithError(conn, "missing machine_key or api_key")
			return
		}

		if err := h.service.gatewayAuth.HandleGatewayAuth(conn, machineKey, apiKey, h.service); err != nil {
			h.disconnectWithError(conn, fmt.Sprintf("authentication failed: %v", err))
			return
		}

		// Successful authentication — set up gateway-specific event handlers
		h.setupGatewayClientEvents(conn)
	})
}

// helper to log, emit error, and disconnect
func (h *Handler) disconnectWithError(conn *socket.Socket, msg string) {
	logger.Warnf("Connection error [%s]: %s", conn.Id(), msg)
	conn.Emit("error", msg)
	// Give the client a brief moment to receive the error before disconnecting
	go func() {
		time.Sleep(50 * time.Millisecond)
		conn.Disconnect(true)
	}()
}

// setupGatewayClientEvents sets up event handlers for individual gateway clients
func (h *Handler) setupGatewayClientEvents(conn *socket.Socket) {
	// Device message handling - 1:1 responses/commands from gateways
	conn.On("device_message", func(datas ...interface{}) {
		if len(datas) == 0 {
			return
		}

		envelope, err := h.parseSocketEnvelope(datas[0])
		if err != nil {
			logger.Warnf("Invalid device message from gateway %s: %v", conn.Id(), err)
			conn.Emit("error", "invalid device message")
			return
		}

		// Process the device message (1:1 routing based on SessionId)
		if err := h.service.ProcessDeviceMessage(conn, envelope); err != nil {
			logger.Warnf("Device message processing failed: %v", err)
			conn.Emit("error", err.Error())
		}
	})

	// Stream-specific events from gateway for broadcast
	conn.On("stream_display", func(datas ...interface{}) {
		if len(datas) == 0 {
			return
		}
		envelope, err := h.parseSocketEnvelope(datas[0])
		if err != nil {
			logger.Warnf("Invalid stream_display from gateway %s: %v", conn.Id(), err)
			conn.Emit("error", "invalid stream_display")
			return
		}
		if err := h.service.ProcessGatewayStream(conn, envelope, domain.StreamTypeDisplay); err != nil {
			logger.Warnf("stream_display processing failed: %v", err)
			conn.Emit("error", err.Error())
		}
	})
	conn.On("stream_rms", func(datas ...interface{}) {
		if len(datas) == 0 {
			return
		}
		envelope, err := h.parseSocketEnvelope(datas[0])
		if err != nil {
			logger.Warnf("Invalid stream_rms from gateway %s: %v", conn.Id(), err)
			conn.Emit("error", "invalid stream_rms")
			return
		}
		if err := h.service.ProcessGatewayStream(conn, envelope, domain.StreamTypeRMS); err != nil {
			logger.Warnf("stream_rms processing failed: %v", err)
			conn.Emit("error", err.Error())
		}
	})

	// Handle disconnect
	conn.On("disconnect", func(datas ...interface{}) {
		logger.Infof("Gateway disconnected: %s", conn.Id())
		h.service.HandleClientDisconnect(string(conn.Id()))
	})
}

// registerFSAEvents sets up event handlers for Field Service App clients
func (h *Handler) registerFSAEvents(namespace string) {
	// FSA namespace handler
	fsaNamespace := h.server.Of(namespace, nil)

	fsaNamespace.On("connection", func(clients ...interface{}) {
		conn := clients[0].(*socket.Socket)
		logger.Infof("FSA connecting: %s", conn.Id())

		// Extract JWT token from connection auth data
		authData := conn.Handshake().Auth
		if authMap, ok := authData.(map[string]interface{}); ok {
			if _, tokenExists := authMap["token"].(string); tokenExists {
				// Authenticate FSA connection (pass service for socket registry)
				if err := h.service.fsaAuth.HandleFSAAuth(conn, authMap, *h.db, h.service); err != nil {
					logger.Warnf("FSA authentication failed: %s - %v", conn.Id(), err)
					h.disconnectWithError(conn, "unauthorized")
					return
				}

				// Set up FSA-specific event handlers
				h.setupFSAClientEvents(conn)
			} else {
				logger.Warnf("FSA connection missing token: %s", conn.Id())
				h.disconnectWithError(conn, "missing token")
				return
			}
		} else {
			logger.Warnf("FSA connection invalid auth data: %s", conn.Id())
			conn.Emit("error", "invalid auth data")
			conn.Disconnect(true)
			return
		}
	})
}

// setupFSAClientEvents sets up event handlers for individual FSA clients
func (h *Handler) setupFSAClientEvents(conn *socket.Socket) {
	// Device channel join
	conn.On("join", func(datas ...interface{}) {
		if len(datas) == 0 {
			conn.Emit("error", "missing room name")
			return
		}

		roomName, ok := datas[0].(string)
		if !ok {
			conn.Emit("error", "invalid room name")
			return
		}

		// Get connection context to validate permissions
		ctx, exists := h.service.GetConnectionContext(string(conn.Id()))
		if !exists {
			conn.Emit("error", "not authenticated")
			return
		}

		// Parse room info and validate provided organization against the device's organization
		roomInfo := domain.ParseChannelInfo(roomName)
		var deviceOrgID string
		if roomInfo.EntityType == "device" && roomInfo.EntityID != "" {
			deviceOrgQuery := `
				SELECT OrganizationId::text as org_id
				FROM {{Device}}
				WHERE Id = $1
			`
			deviceRow, err := (*h.db).QueryRow(deviceOrgQuery, roomInfo.EntityID)
			if err != nil {
				logger.Warnf("Failed to get device organization for %s: %v", roomInfo.EntityID, err)
				conn.Emit("error", "invalid device organization")
				return
			}
			if v, ok := deviceRow["org_id"].(string); ok {
				deviceOrgID = v
			}
			if deviceOrgID == "" {
				conn.Emit("error", "invalid device organization")
				return
			}

			// If client supplied an org in the room string, it must match the device's org
			if roomInfo.OrgID != "" && roomInfo.OrgID != deviceOrgID {
				logger.Warnf("Room join org mismatch for %s: roomOrg=%s deviceOrg=%s", conn.Id(), roomInfo.OrgID, deviceOrgID)
				conn.Emit("error", "organization mismatch")
				return
			}
		}

		// Validate room access permissions
		if err := h.service.permissionChecker.CheckRoomAccess(ctx, roomName); err != nil {
			logger.Warnf("Room join denied for %s: %v", conn.Id(), err)
			conn.Emit("error", "access denied")
			return
		}

		// If this is a device channel, start streaming (service will join canonical room)
		if deviceOrgID == "" {
			conn.Emit("error", "invalid device organization")
			return
		}
		if err := h.service.HandleDeviceChannelJoin(conn, roomName, deviceOrgID); err != nil {
			logger.Warnf("Device channel join failed for %s: %v", conn.Id(), err)
			conn.Emit("error", err.Error())
			return
		}

		// Emit joined with canonical room name (based on device's organization if available)
		canonical := domain.GetDeviceStreamChannel(deviceOrgID, roomInfo.EntityID, roomInfo.Subtype)
		logger.Infof("FSA %s joined canonical channel %s", conn.Id(), canonical)
		conn.Emit("joined", canonical)
	})

	// Device channel leave
	conn.On("leave", func(datas ...interface{}) {
		if len(datas) == 0 {
			conn.Emit("error", "missing room name")
			return
		}

		roomName, ok := datas[0].(string)
		if !ok {
			conn.Emit("error", "invalid room name")
			return
		}

		// Parse room info and validate provided organization against the device's organization
		roomInfo := domain.ParseChannelInfo(roomName)
		var deviceOrgID string
		if roomInfo.EntityType == "device" && roomInfo.EntityID != "" {
			deviceOrgQuery := `
				SELECT OrganizationId::text as org_id
				FROM {{Device}}
				WHERE Id = $1
			`
			deviceRow, err := (*h.db).QueryRow(deviceOrgQuery, roomInfo.EntityID)
			if err != nil {
				logger.Warnf("Failed to get device organization for %s: %v", roomInfo.EntityID, err)
				conn.Emit("error", "invalid device organization")
				return
			}
			if v, ok := deviceRow["org_id"].(string); ok {
				deviceOrgID = v
			}
			if deviceOrgID == "" {
				conn.Emit("error", "invalid device organization")
				return
			}

			if roomInfo.OrgID != "" && roomInfo.OrgID != deviceOrgID {
				logger.Warnf("Room leave org mismatch for %s: roomOrg=%s deviceOrg=%s", conn.Id(), roomInfo.OrgID, deviceOrgID)
				conn.Emit("error", "organization mismatch")
				return
			}
		}

		// If this is a device channel, stop streaming
		if deviceOrgID == "" {
			conn.Emit("error", "invalid device organization")
			return
		}
		if err := h.service.HandleDeviceChannelLeave(conn, roomName, deviceOrgID); err != nil {
			logger.Warnf("Device channel leave failed for %s: %v", conn.Id(), err)
			// Don't emit error for leave operations
		}

		// Emit left with canonical room name
		canonical := domain.GetDeviceStreamChannel(deviceOrgID, roomInfo.EntityID, roomInfo.Subtype)
		logger.Infof("FSA %s left canonical channel %s", conn.Id(), canonical)
		conn.Emit("left", canonical)
	})

	// Device request/command handling
	conn.On("device_request", func(datas ...interface{}) {
		if len(datas) == 0 {
			return
		}

		envelope, err := h.parseSocketEnvelope(datas[0])
		if err != nil {
			logger.Warnf("Invalid device request from FSA %s: %v", conn.Id(), err)
			conn.Emit("error", "invalid device request")
			return
		}

		if err := h.service.ProcessDeviceRequest(conn, envelope); err != nil {
			logger.Warnf("Device request processing failed for %s: %v", conn.Id(), err)
			conn.Emit("error", err.Error())
			return
		}
	})

	// Handle disconnect - clean up stream subscriptions
	conn.On("disconnect", func(datas ...interface{}) {
		logger.Infof("FSA disconnected: %s", conn.Id())
		h.service.HandleClientDisconnect(string(conn.Id()))
	})
}

// registerBasicClientEvents sets up basic event handlers for the root namespace
func (h *Handler) registerBasicClientEvents(namespace string) {
	// Root namespace handler
	rootNamespace := h.server.Of(namespace, nil)

	rootNamespace.On("connection", func(clients ...interface{}) {
		conn := clients[0].(*socket.Socket)
		logger.Infof("Client connected to root namespace: %s", conn.Id())

		// Basic ping handler
		conn.On("ping", func(datas ...interface{}) {
			conn.Emit("pong", "pong")
		})

		// Handle disconnect
		conn.On("disconnect", func(datas ...interface{}) {
			logger.Infof("Client disconnected from root namespace: %s", conn.Id())
		})
	})
}

// parseSocketEnvelope parses incoming data into a SocketEnvelope
func (h *Handler) parseSocketEnvelope(data interface{}) (*domain.SocketEnvelope, error) {
	envelope := &domain.SocketEnvelope{}

	switch v := data.(type) {
	case []byte:
		// Binary format: expect protobuf-serialized SocketEnvelope
		var pbEnv domain.SocketEnvelope
		if err := proto.Unmarshal(v, &pbEnv); err == nil {
			return &pbEnv, nil
		}
		return nil, fmt.Errorf("failed to parse binary envelope as protobuf SocketEnvelope")
	case string:
		// JSON string
		if err := json.Unmarshal([]byte(v), envelope); err != nil {
			return nil, fmt.Errorf("failed to parse JSON string: %w", err)
		}
	case map[string]interface{}:
		// JSON object
		jsonData, err := json.Marshal(v)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal map: %w", err)
		}
		if err := json.Unmarshal(jsonData, envelope); err != nil {
			return nil, fmt.Errorf("failed to parse JSON object: %w", err)
		}
	default:
		// Attempt to handle binary buffer wrappers that expose Bytes() []byte
		type hasBytes interface{ Bytes() []byte }
		if buf, ok := any(v).(hasBytes); ok {
			b := buf.Bytes()
			var pbEnv domain.SocketEnvelope
			if err := proto.Unmarshal(b, &pbEnv); err == nil {
				return &pbEnv, nil
			}
			return nil, fmt.Errorf("failed to parse binary envelope as protobuf SocketEnvelope")
		}
		return nil, fmt.Errorf("unsupported data type: %T", v)
	}

	return envelope, nil
}
