package socketio

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	"synapse-its.com/rushhour/domain"
)

// buffer type implementing Bytes() []byte for the Bytes()-based branch
type testBuf struct{ b []byte }

func (t testBuf) Bytes() []byte { return t.b }

func TestHandler_parseSocketEnvelope_JSONString(t *testing.T) {
	// Arrange
	h := &Handler{}
	jsonStr := `{"type":1,"device_id":"dev-1","request_id":123}`

	// Act
	env, err := h.parseSocketEnvelope(jsonStr)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, domain.EnvelopeTypeWrapperCommand, env.Type)
	assert.Equal(t, "dev-1", env.DeviceId)
	assert.EqualValues(t, 123, env.RequestId)
}

func TestHandler_parseSocketEnvelope_JSONObject(t *testing.T) {
	h := &Handler{}
	obj := map[string]interface{}{
		"type":       float64(domain.EnvelopeTypeWrapperCommand),
		"device_id":  "dev-2",
		"request_id": float64(456),
	}
	env, err := h.parseSocketEnvelope(obj)
	assert.NoError(t, err)
	assert.Equal(t, domain.EnvelopeTypeWrapperCommand, env.Type)
	assert.Equal(t, "dev-2", env.DeviceId)
	assert.EqualValues(t, 456, env.RequestId)
}

func TestHandler_parseSocketEnvelope_BinaryProtobuf(t *testing.T) {
	h := &Handler{}
	pb := &domain.SocketEnvelope{
		Type:      domain.EnvelopeTypeWrapperCommand,
		DeviceId:  "dev-3",
		RequestId: 789,
	}
	b, err := proto.Marshal(pb)
	assert.NoError(t, err)

	env, err := h.parseSocketEnvelope(b)
	assert.NoError(t, err)
	assert.Equal(t, pb.Type, env.Type)
	assert.Equal(t, pb.DeviceId, env.DeviceId)
	assert.EqualValues(t, pb.RequestId, env.RequestId)
}

func TestHandler_parseSocketEnvelope_BinaryWrapperBytes(t *testing.T) {
	h := &Handler{}
	pb := &domain.SocketEnvelope{
		Type:      domain.EnvelopeTypeWrapperResponse,
		DeviceId:  "dev-4",
		RequestId: 1001,
	}
	b, err := proto.Marshal(pb)
	assert.NoError(t, err)

	buf := testBuf{b: b}
	env, err := h.parseSocketEnvelope(buf)
	assert.NoError(t, err)
	assert.Equal(t, pb.Type, env.Type)
	assert.Equal(t, pb.DeviceId, env.DeviceId)
	assert.EqualValues(t, pb.RequestId, env.RequestId)
}

func TestHandler_parseSocketEnvelope_InvalidBinaryReturnsError(t *testing.T) {
	h := &Handler{}
	invalid := []byte{0x00, 0x01, 0x02}
	_, err := h.parseSocketEnvelope(invalid)
	assert.Error(t, err)
}

func TestHandler_parseSocketEnvelope_UnsupportedType(t *testing.T) {
	h := &Handler{}
	_, err := h.parseSocketEnvelope(12345)
	assert.Error(t, err)
}
