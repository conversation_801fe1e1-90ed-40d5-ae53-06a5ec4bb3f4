package socketio

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/zishang520/socket.io-go-redis/adapter"
	"github.com/zishang520/socket.io-go-redis/types"
	"github.com/zishang520/socket.io/v2/socket"
	"google.golang.org/protobuf/proto"

	"synapse-its.com/rushhour/auth"
	"synapse-its.com/rushhour/domain"
	"synapse-its.com/rushhour/permissions"
	"synapse-its.com/rushhour/tracking"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// Service handles Socket.io business logic and manages connections
type Service struct {
	ctx               context.Context
	server            *socket.Server
	socketRegistry    *domain.SocketRegistry // Socket-centric connection tracking
	streamTracker     *tracking.StreamTracker
	permissionChecker *permissions.PermissionChecker
	gatewayAuth       *auth.GatewayAuthenticator
	fsaAuth           *auth.FSAAuthenticator
	binaryHandler     *BinaryMessageHandler
	db                *connect.DatabaseExecutor
	redisClient       *redis.Client // Redis client for business logic caching
}

// NewService creates a new Socket.io service
func NewService(ctx context.Context, db *connect.DatabaseExecutor) (*Service, error) {
	// Initialize Socket.io server with Redis adapter
	server, err := createSocketIOServerWithRedis(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create Socket.IO server: %v", err)
	}

	// Create Redis client for business logic caching (separate from Socket.IO adapter)
	var redisClient *redis.Client
	redisAddr := os.Getenv("RH_REDIS")
	if redisAddr != "" {
		redisClient = redis.NewClient(&redis.Options{
			Addr:     redisAddr,
			Username: "",
			Password: "",
			DB:       0, // IMPORTANT: use the SAME DB index as the Socket.IO adapter (DB 0) to keep
			// adapter-managed and service-managed data aligned for recovery and coordination
		})

		// Test Redis connection
		if err := redisClient.Ping(ctx).Err(); err != nil {
			logger.Warnf("Failed to connect to Redis for business logic caching: %v", err)
			redisClient = nil // Fall back to database-only mode
		} else {
			logger.Info("Connected to Redis for business logic caching")
		}
	} else {
		logger.Warn("RH_REDIS not set, running without Redis caching for business logic")
	}

	// Initialize components
	socketRegistry := domain.NewSocketRegistry() // Socket registry for direct messaging

	// Initialize stream tracker with Redis support if available
	var streamTracker *tracking.StreamTracker
	if redisClient != nil {
		streamTracker = tracking.NewStreamTrackerWithRedis(redisClient, ctx)
		logger.Info("Initialized stream tracker with Redis support for horizontal scaling")
	} else {
		streamTracker = tracking.NewStreamTracker()
		logger.Info("Initialized stream tracker with local storage (single instance mode)")
	}

	permissionChecker := permissions.NewPermissionChecker()
	gatewayAuth := auth.NewGatewayAuthenticator(*db)
	fsaAuth := auth.NewFSAAuthenticator(*db)
	binaryHandler := NewBinaryMessageHandler()

	service := &Service{
		ctx:               ctx,
		server:            server,
		socketRegistry:    socketRegistry, // Include socket registry
		streamTracker:     streamTracker,
		permissionChecker: permissionChecker,
		gatewayAuth:       gatewayAuth,
		fsaAuth:           fsaAuth,
		binaryHandler:     binaryHandler,
		db:                db,
		redisClient:       redisClient, // Redis client for business logic caching
	}

	logger.Info("Socket.io service initialized with socket registry")
	return service, nil
}

// GetServer returns the Socket.io server instance
func (s *Service) GetServer() *socket.Server {
	return s.server
}

// GetConnectionContext retrieves connection context by socket ID
// Uses Redis-backed storage for horizontal scaling compatibility
func (s *Service) GetConnectionContext(socketID string) (*domain.ConnectionContext, bool) {
	// Try Redis cache first (cross-instance lookup)
	if s.redisClient != nil {
		cacheKey := fmt.Sprintf("rushhour:session:%s", socketID)
		ctxData, err := s.redisClient.Get(s.ctx, cacheKey).Result()
		if err == redis.Nil {
			// Not found in Redis
			return nil, false
		} else if err != nil {
			logger.Warnf("Redis error for session %s lookup: %v", socketID, err)
			// Fall back to local lookup
		} else {
			// Deserialize from Redis
			var ctx domain.ConnectionContext
			if err := json.Unmarshal([]byte(ctxData), &ctx); err != nil {
				logger.Warnf("Failed to deserialize session context for %s: %v", socketID, err)
				// Fall back to local lookup
			} else {
				logger.Debugf("Found Redis cached session for socket %s", socketID)
				return &ctx, true
			}
		}
	}

	// Fall back to local socket registry (single instance mode)
	return s.socketRegistry.GetContext(socketID)
}

// RegisterGateway registers a gateway connection in Redis for cross-instance routing
func (s *Service) RegisterGateway(gatewayID, socketID string) {
	// Register in Redis for cross-instance lookup
	if s.redisClient != nil {
		cacheKey := fmt.Sprintf("rushhour:gateway:%s:socket", gatewayID)
		ttl := 1 * time.Hour // Long TTL, will be refreshed by heartbeat or removed on disconnect
		if err := s.redisClient.Set(s.ctx, cacheKey, socketID, ttl).Err(); err != nil {
			logger.Warnf("Failed to register gateway %s in Redis: %v", gatewayID, err)
		} else {
			logger.Debugf("Registered gateway in Redis: gateway=%s -> socket=%s", gatewayID, socketID)
		}
	}
}

// UnregisterGateway removes a gateway connection from Redis
func (s *Service) UnregisterGateway(gatewayID string) {
	// NOTE: Local registry gateway cleanup removed - now Redis-only for cross-instance support

	// Remove from Redis
	if s.redisClient != nil {
		cacheKey := fmt.Sprintf("rushhour:gateway:%s:socket", gatewayID)
		if err := s.redisClient.Del(s.ctx, cacheKey).Err(); err != nil {
			logger.Warnf("Failed to unregister gateway %s from Redis: %v", gatewayID, err)
		} else {
			logger.Debugf("Unregistered gateway from Redis: gateway=%s", gatewayID)
		}
	} else {
	}
}

// RecoverActiveStreamsForGateway checks for active device rooms and sends stream_control commands
// to a reconnecting gateway for any devices that have active listeners
func (s *Service) RecoverActiveStreamsForGateway(gatewayID string) {
	// Get gateway organization and devices in one efficient call
	gatewayInfo, err := s.getGatewayInfoForRecovery(gatewayID)
	if err != nil {
		logger.Warnf("Failed to get gateway info for %s during recovery: %v", gatewayID, err)
		return
	}

	if len(gatewayInfo.Devices) == 0 {
		return
	}

	// Build all possible room keys for batch checking
	var roomKeys []string
	deviceRoomMap := make(map[string]struct {
		DeviceID   string
		StreamType string
	})

	for _, deviceID := range gatewayInfo.Devices {
		// Check both stream types (display and rms)
		for _, streamType := range []string{"stream_display", "stream_rms"} {
			roomKey := fmt.Sprintf("org:%s:device:%s:%s", gatewayInfo.OrgID, deviceID, streamType)
			roomKeys = append(roomKeys, roomKey)
			deviceRoomMap[roomKey] = struct {
				DeviceID   string
				StreamType string
			}{DeviceID: deviceID, StreamType: streamType}
		}
	}

	// Check all room keys in a single Redis call
	activeRooms, err := s.checkRoomKeysExist(roomKeys)
	if err != nil {
		logger.Warnf("Failed to check active rooms during recovery: %v", err)
		return
	}

	// Send recovery commands for active rooms
	recoveredStreams := 0
	for _, roomKey := range activeRooms {
		deviceInfo := deviceRoomMap[roomKey]

		// Send stream_control command to gateway to start streaming
		if err := s.sendStreamControlToGatewayOptimized(gatewayID, deviceInfo.DeviceID, deviceInfo.StreamType, "start_stream"); err != nil {
			logger.Warnf("Failed to recover stream for device %s: %v", deviceInfo.DeviceID, err)
		} else {
			recoveredStreams++
		}
	}

	if recoveredStreams > 0 {
		logger.Infof("Gateway %s reconnected - recovered %d active streams", gatewayID, recoveredStreams)
	}
}

// GatewayRecoveryInfo holds gateway organization and device information for recovery
type GatewayRecoveryInfo struct {
	OrgID   string
	Devices []string
}

// getGatewayInfoForRecovery efficiently gets gateway organization and devices in one call
func (s *Service) getGatewayInfoForRecovery(gatewayID string) (*GatewayRecoveryInfo, error) {
	if gatewayID == "" {
		return nil, fmt.Errorf("gateway ID cannot be empty")
	}

	// First get the gateway's organization ID
	gatewayQuery := `
		SELECT OrganizationId::text as org_id
		FROM {{SoftwareGateway}} 
		WHERE Id = $1 AND IsEnabled = true
	`

	gatewayRow, err := (*s.db).QueryRow(gatewayQuery, gatewayID)
	if err != nil {
		return nil, fmt.Errorf("failed to get gateway organization: %w", err)
	}

	orgID, ok := gatewayRow["org_id"].(string)
	if !ok {
		return nil, fmt.Errorf("gateway %s has invalid organization assignment", gatewayID)
	}
	if orgID == "" {
		return nil, fmt.Errorf("gateway %s has no assigned organization", gatewayID)
	}

	// Now get all devices for this gateway
	deviceQuery := `
		SELECT Id::text as device_id
		FROM {{Device}} 
		WHERE SoftwareGatewayId = $1 AND IsEnabled = true AND NOT IsDeleted
	`

	rows, err := (*s.db).QueryGeneric(deviceQuery, gatewayID)
	if err != nil {
		return nil, fmt.Errorf("failed to query devices for gateway: %w", err)
	}

	var devices []string
	for _, row := range rows {
		deviceID, ok := row["device_id"].(string)
		if !ok {
			continue
		}
		if deviceID != "" {
			devices = append(devices, deviceID)
		}
	}

	info := &GatewayRecoveryInfo{
		OrgID:   orgID,
		Devices: devices,
	}

	return info, nil
}

// getDevicesForGateway queries the database to get all devices assigned to a gateway
func (s *Service) getDevicesForGateway(gatewayID string) ([]string, error) {
	if gatewayID == "" {
		return nil, fmt.Errorf("gateway ID cannot be empty")
	}

	// Query the Device table to get all devices for this gateway
	query := `
		SELECT Id::text as device_id
		FROM {{Device}} 
		WHERE SoftwareGatewayId = $1 AND IsEnabled = true AND NOT IsDeleted
	`

	rows, err := (*s.db).QueryGeneric(query, gatewayID)
	if err != nil {
		return nil, fmt.Errorf("failed to query devices for gateway: %w", err)
	}

	var devices []string
	for _, row := range rows {
		deviceID, ok := row["device_id"].(string)
		if !ok {
			continue
		}
		if deviceID != "" {
			devices = append(devices, deviceID)
		}
	}

	return devices, nil
}

// checkRoomKeysExist efficiently checks multiple room keys in Redis using batch operations
func (s *Service) checkRoomKeysExist(roomKeys []string) ([]string, error) {
	if s.redisClient == nil {
		return nil, fmt.Errorf("Redis client not available")
	}

	if len(roomKeys) == 0 {
		return []string{}, nil
	}

	// Use Redis EXISTS to check multiple keys at once
	// EXISTS returns the number of keys that exist, but we need which ones exist
	// So we'll use a pipeline to check each key individually but in one batch
	pipe := s.redisClient.Pipeline()
	cmds := make([]*redis.IntCmd, len(roomKeys))

	for i, key := range roomKeys {
		cmds[i] = pipe.Exists(s.ctx, key)
	}

	_, err := pipe.Exec(s.ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to check room keys: %w", err)
	}

	var existingKeys []string
	for i, cmd := range cmds {
		if cmd.Val() > 0 {
			existingKeys = append(existingKeys, roomKeys[i])
		} else {
		}
	}

	return existingKeys, nil
}

// sendStreamControlToGatewayOptimized sends a stream_control command with exact room name (more efficient)
func (s *Service) sendStreamControlToGatewayOptimized(gatewayID, deviceID, streamType, action string) error {
	// Get the gateway socket ID from Redis
	gatewaySocketID, err := s.getGatewaySocketID(gatewayID)
	if err != nil {
		return fmt.Errorf("failed to find gateway socket: %w", err)
	}

	// Build the stream control message with exact room name
	streamControlData := map[string]interface{}{
		"action":      action,
		"device_id":   deviceID,
		"stream_type": streamType,
	}

	// Send to gateway namespace
	gatewayNamespace := s.server.Of("/auth/gateway", nil)
	if gatewayNamespace == nil {
		return fmt.Errorf("gateway namespace not found")
	}

	// Send to the specific gateway socket
	gatewayNamespace.To(socket.Room(gatewaySocketID)).Emit("stream_control", streamControlData)

	return nil
}

// StoreConnectionContext stores connection context in Redis for cross-instance access
func (s *Service) StoreConnectionContext(socketID string, ctx *domain.ConnectionContext) error {
	// Store in local registry for backward compatibility and single-instance mode
	s.socketRegistry.RegisterSocket(socketID, ctx)

	// Store in Redis for cross-instance access
	if s.redisClient != nil {
		cacheKey := fmt.Sprintf("rushhour:session:%s", socketID)
		ttl := 2 * time.Hour // Session TTL

		// Serialize context to JSON
		ctxData, err := json.Marshal(ctx)
		if err != nil {
			logger.Warnf("Failed to serialize connection context for %s: %v", socketID, err)
			return err
		}

		if err := s.redisClient.Set(s.ctx, cacheKey, string(ctxData), ttl).Err(); err != nil {
			logger.Warnf("Failed to store session context in Redis for %s: %v", socketID, err)
			return err
		}

		logger.Debugf("Stored session context in Redis: socket=%s", socketID)
	}

	return nil
}

// RemoveConnectionContext removes connection context from Redis and local registry
func (s *Service) RemoveConnectionContext(socketID string) {
	// Remove from local registry
	s.socketRegistry.UnregisterSocket(socketID)

	// Remove from Redis
	if s.redisClient != nil {
		cacheKey := fmt.Sprintf("rushhour:session:%s", socketID)
		if err := s.redisClient.Del(s.ctx, cacheKey).Err(); err != nil {
			logger.Warnf("Failed to remove session context from Redis for %s: %v", socketID, err)
		} else {
			logger.Debugf("Removed session context from Redis: socket=%s", socketID)
		}
	}
}

// validateDeviceMessageEnvelope ensures device_message envelopes contain required fields.
// Currently requires a non-empty SessionId for 1:1 routing.
func (s *Service) validateDeviceMessageEnvelope(envelope *domain.SocketEnvelope) error {
	if envelope == nil {
		return fmt.Errorf("nil envelope")
	}
	if envelope.SessionId == "" {
		return fmt.Errorf("missing session id for device_message")
	}
	if envelope.UserId == "" {
		return fmt.Errorf("missing user id for device_message")
	}
	return nil
}

// validateGatewayStreamEnvelope ensures gateway stream envelopes do not carry SessionId or UserId.
func (s *Service) validateGatewayStreamEnvelope(envelope *domain.SocketEnvelope) error {
	if envelope == nil {
		return fmt.Errorf("nil envelope")
	}
	if envelope.SessionId != "" {
		return fmt.Errorf("session id must not be set for gateway stream events")
	}
	if envelope.UserId != "" {
		return fmt.Errorf("user id must not be set for gateway stream events")
	}
	return nil
}

// validateFSADeviceRequestEnvelope ensures FSA/Onramp device requests do not carry SessionId or UserId.
func (s *Service) validateFSADeviceRequestEnvelope(envelope *domain.SocketEnvelope) error {
	if envelope == nil {
		return fmt.Errorf("nil envelope")
	}
	if envelope.SessionId != "" {
		return fmt.Errorf("session id must not be set by client for device_request")
	}
	if envelope.UserId != "" {
		return fmt.Errorf("user id must not be set by client for device_request")
	}
	return nil
}

// ProcessDeviceMessage handles all device communication from gateways
// Direct socket messaging to specific FSA/Onramp clients
// This implements the Gateway->RH->FSA flow with envelope enrichment
func (s *Service) ProcessDeviceMessage(conn *socket.Socket, envelope *domain.SocketEnvelope) error {
	ctx, exists := s.socketRegistry.GetContext(string(conn.Id()))
	if !exists || ctx.AuthInfo == nil || ctx.AuthInfo.ClientType != domain.ClientTypeGateway {
		return fmt.Errorf("unauthorized: not a gateway connection")
	}

	// Validate device access
	if err := s.permissionChecker.CheckDeviceAccess(ctx, envelope.DeviceId, "message"); err != nil {
		return fmt.Errorf("access denied to device %s: %v", envelope.DeviceId, err)
	}

	// device_message must always include a SessionId and UserId; otherwise error
	if err := s.validateDeviceMessageEnvelope(envelope); err != nil {
		return err
	}

	// Optional hardening: verify the session belongs to the provided user
	if sessCtx, ok := s.GetConnectionContext(envelope.SessionId); ok && sessCtx.AuthInfo != nil {
		if sessCtx.AuthInfo.UserID != "" && envelope.UserId != "" && sessCtx.AuthInfo.UserID != envelope.UserId {
			return fmt.Errorf("session/user mismatch for device_message: session=%s user_in_ctx=%s user_in_envelope=%s", envelope.SessionId, sessCtx.AuthInfo.UserID, envelope.UserId)
		}
	}

	// Enrich envelope with gateway origin before forwarding to FSA/Onramp
	enrichedEnvelope := proto.Clone(envelope).(*domain.SocketEnvelope)
	enrichedEnvelope.Origin = domain.OriginTypeGateway

	// Route directly to the session that originated the command via FSA namespace
	fsaNamespace := s.server.Of(domain.NamespaceFSA, nil)
	fsaNamespace.To(socket.Room(envelope.SessionId)).Emit("device_message", enrichedEnvelope)

	return nil
}

// ProcessGatewayStream handles streaming data events from gateways for broadcast delivery.
// Gateways emit stream-specific events (stream_display, stream_rms). RH enriches and broadcasts
// to the canonical device stream room without inspecting payload contents.
func (s *Service) ProcessGatewayStream(conn *socket.Socket, envelope *domain.SocketEnvelope, streamType string) error {
	// Ensure this is a gateway connection
	ctx, exists := s.socketRegistry.GetContext(string(conn.Id()))
	if !exists || ctx.AuthInfo == nil || ctx.AuthInfo.ClientType != domain.ClientTypeGateway {
		return fmt.Errorf("unauthorized: not a gateway connection")
	}

	// Validate device access
	if err := s.permissionChecker.CheckDeviceAccess(ctx, envelope.DeviceId, "message"); err != nil {
		return fmt.Errorf("access denied to device %s: %v", envelope.DeviceId, err)
	}

	// Validate envelope does not improperly include session/user for stream events
	if err := s.validateGatewayStreamEnvelope(envelope); err != nil {
		return err
	}

	// Get connection context to determine organization for room routing
	gatewayCtx, exists := s.GetConnectionContext(string(conn.Id()))
	if !exists {
		return fmt.Errorf("failed to get gateway context for streaming: socket not found")
	}

	// Enrich envelope with gateway origin before broadcasting
	enrichedEnvelope := proto.Clone(envelope).(*domain.SocketEnvelope)
	enrichedEnvelope.Origin = domain.OriginTypeGateway

	// Broadcast to the stream-specific room
	fsaNamespace := s.server.Of(domain.NamespaceFSA, nil)
	deviceRoom := domain.GetDeviceStreamChannel(gatewayCtx.OrgID, enrichedEnvelope.DeviceId, streamType)
	fsaNamespace.To(socket.Room(deviceRoom)).Emit("device_message", enrichedEnvelope)

	return nil
}

// ProcessDeviceRequest handles device control/command messages from FSA and Onramp clients
// This implements the FSA->RH->Gateway flow with envelope enrichment
// NOTE: Both FSA and Onramp clients can send device commands. Onramp users (technical support)
// may actually have greater permissions than field techs. Granular permission filtering
// will be implemented later - for now using existing JWT/Token auth and permission system.
func (s *Service) ProcessDeviceRequest(conn *socket.Socket, envelope *domain.SocketEnvelope) error {
	ctx, exists := s.socketRegistry.GetContext(string(conn.Id()))
	if !exists || ctx.AuthInfo == nil || ctx.AuthInfo.Permissions == nil ||
		(ctx.AuthInfo.ClientType != domain.ClientTypeFSA && ctx.AuthInfo.ClientType != domain.ClientTypeOnramp) {
		logger.Warnf("Unauthorized: not an FSA or Onramp connection: %v", ctx.AuthInfo)
		return ErrUnauthorized
	}

	// Validate that clients do not set SessionId or UserId on device_request; RH assigns them
	if err := s.validateFSADeviceRequestEnvelope(envelope); err != nil {
		return err
	}

	// Strict org consistency: resolve device's organization and verify envelope org matches (if provided)
	deviceOrgQuery := `
		SELECT OrganizationId::text as org_id
		FROM {{Device}}
		WHERE Id = $1
	`
	deviceRow, err := (*s.db).QueryRow(deviceOrgQuery, envelope.DeviceId)
	if err != nil {
		return fmt.Errorf("failed to get device organization for %s: %w", envelope.DeviceId, err)
	}
	deviceOrgID, ok := deviceRow["org_id"].(string)
	if !ok || deviceOrgID == "" {
		return fmt.Errorf("device %s has invalid or missing organization assignment", envelope.DeviceId)
	}
	if envelope.OrganizationId != "" && envelope.OrganizationId != deviceOrgID {
		return fmt.Errorf("organization mismatch: envelope=%s, device=%s", envelope.OrganizationId, deviceOrgID)
	}

	// Validate device access using existing permission system
	// TODO: Implement granular command-level permissions when spec is finalized
	if err := s.permissionChecker.CheckDeviceAccess(ctx, envelope.DeviceId, "control"); err != nil {
		logger.Warnf("Access denied to device %s: %v", envelope.DeviceId, err)
		return ErrDeviceAccessDenied
	}

	// Validate WrapperCommand permissions by inspecting inner payload (no outer re-unmarshal)
	if envelope.Type == domain.EnvelopeTypeWrapperCommand && len(envelope.Payload) > 0 {
		if err := s.binaryHandler.validateDeviceCommand(
			envelope,
			ctx.AuthInfo.Permissions,
			*s.db,
		); err != nil {
			logger.Warnf("Command permission validation failed for device %s: %v", envelope.DeviceId, err)
			return ErrCommandNotPermitted
		}
	}

	// RH FILLS IN MISSING INFO: Enhance envelope with user/session/routing information
	enrichedEnvelope := proto.Clone(envelope).(*domain.SocketEnvelope) // Proper protobuf clone
	enrichedEnvelope.Origin = domain.OriginTypeFSA
	if ctx.AuthInfo.ClientType == domain.ClientTypeOnramp {
		enrichedEnvelope.Origin = domain.OriginTypeOnramp
	}

	// Fill in Session ID and User ID for proper routing and tracking
	enrichedEnvelope.SessionId = string(conn.Id()) // Use Socket ID as Session ID for routing responses back
	enrichedEnvelope.UserId = ctx.AuthInfo.UserID  // User ID for authorization and auditing
	// Set OrganizationId strictly to the device's org for consistency
	enrichedEnvelope.OrganizationId = deviceOrgID

	// Find the gateway responsible for this device (database-first with caching)
	gatewaySocketID, err := s.GetGatewaySocketForDevice(envelope.DeviceId)
	if err != nil {
		return fmt.Errorf("failed to find gateway for device %s: %w", envelope.DeviceId, err)
	}

	// DIRECT SOCKET MESSAGING: Send directly to the specific gateway socket
	// Emit to the gateway namespace (not main namespace)
	gatewayNamespace := s.server.Of(domain.NamespaceGateway, nil)
	gatewayNamespace.To(socket.Room(gatewaySocketID)).Emit("device_request", &enrichedEnvelope)

	return nil
}

// HandleDeviceChannelJoin handles FSA clients joining device channels (replaces watch_stream)
// Uses socket registry for connection tracking and room management for streaming
func (s *Service) HandleDeviceChannelJoin(conn *socket.Socket, roomName, orgID string) error {
	// Parse the room name to extract device information
	roomInfo := domain.ParseChannelInfo(roomName)

	// Only handle device stream channels (both stream_display and stream_rms)
	if roomInfo.EntityType != "device" ||
		(roomInfo.Subtype != domain.StreamTypeDisplay && roomInfo.Subtype != domain.StreamTypeRMS) {
		// Not a device stream channel, no action needed
		return nil
	}

	deviceID := roomInfo.EntityID
	if deviceID == "" {
		return fmt.Errorf("invalid device channel format")
	}

	// Get connection context from socket registry
	ctx, exists := s.socketRegistry.GetContext(string(conn.Id()))
	if !exists {
		return fmt.Errorf("connection context not found")
	}

	// CRITICAL: Only FSA and Onramp clients should be counted as viewers, never gateways
	if ctx.AuthInfo == nil ||
		(ctx.AuthInfo.ClientType != domain.ClientTypeFSA && ctx.AuthInfo.ClientType != domain.ClientTypeOnramp) {
		return fmt.Errorf("only FSA and Onramp clients can join device stream channels")
	}

	// Validate device access for streaming (check specific stream type permissions)
	streamAction := "stream"
	if roomInfo.Subtype == domain.StreamTypeRMS {
		streamAction = "stream_rms"
	} else if roomInfo.Subtype == domain.StreamTypeDisplay {
		streamAction = "stream_display"
	}

	if err := s.permissionChecker.CheckDeviceAccess(ctx, deviceID, streamAction); err != nil {
		return fmt.Errorf("access denied to device %s stream type %s: %v", deviceID, roomInfo.Subtype, err)
	}

	// Track the stream subscription and check if this is the first viewer for this stream type
	// NOTE: Only FSA and Onramp clients reach this point, so viewer count is accurate
	streamKey := fmt.Sprintf("%s:%s", deviceID, roomInfo.Subtype)
	isFirstViewer := s.streamTracker.AddViewer(streamKey, string(conn.Id()))

	// STREAMING ROOMS: Join the actual streaming room for data delivery
	// This handles the broadcast case - streaming data from device goes to all room members
	streamRoom := domain.GetDeviceStreamChannel(orgID, deviceID, roomInfo.Subtype)
	conn.Join(socket.Room(streamRoom))

	// Notify gateway to start streaming for this device stream type (if this is the first viewer)
	if isFirstViewer {
		s.notifyGatewayStreamStartWithRetry(deviceID, orgID, roomInfo.Subtype)
	}

	logger.Debugf("FSA/Onramp client %s joined device %s stream %s (first viewer: %v)",
		conn.Id(), deviceID, roomInfo.Subtype, isFirstViewer)
	return nil
}

// HandleDeviceChannelLeave handles FSA clients leaving device channels (replaces unwatch_stream)
// Uses socket registry for connection tracking and room management for streaming
func (s *Service) HandleDeviceChannelLeave(conn *socket.Socket, roomName, orgID string) error {
	// Parse the room name to extract device information
	roomInfo := domain.ParseChannelInfo(roomName)

	// Only handle device stream channels (both stream_display and stream_rms)
	if roomInfo.EntityType != "device" ||
		(roomInfo.Subtype != domain.StreamTypeDisplay && roomInfo.Subtype != domain.StreamTypeRMS) {
		// Not a device stream channel, no action needed
		return nil
	}

	deviceID := roomInfo.EntityID
	if deviceID == "" {
		return fmt.Errorf("invalid device channel format")
	}

	// Get connection context from socket registry
	ctx, exists := s.socketRegistry.GetContext(string(conn.Id()))
	if !exists {
		return fmt.Errorf("connection context not found")
	}

	// CRITICAL: Only FSA and Onramp clients should be tracked as viewers
	if ctx.AuthInfo == nil ||
		(ctx.AuthInfo.ClientType != domain.ClientTypeFSA && ctx.AuthInfo.ClientType != domain.ClientTypeOnramp) {
		return fmt.Errorf("only FSA and Onramp clients can leave device stream channels")
	}

	// Track the stream subscription and check if this was the last viewer for this stream type
	streamKey := fmt.Sprintf("%s:%s", deviceID, roomInfo.Subtype)
	isLastViewer := s.streamTracker.RemoveViewer(streamKey, string(conn.Id()))

	// STREAMING ROOMS: Leave the actual streaming room
	streamRoom := domain.GetDeviceStreamChannel(orgID, deviceID, roomInfo.Subtype)
	conn.Leave(socket.Room(streamRoom))

	// Notify gateway to stop streaming for this device stream type (if this was the last viewer)
	if isLastViewer {
		if err := s.notifyGatewayStreamStop(deviceID, orgID, roomInfo.Subtype); err != nil {
			logger.Warnf("Failed to notify gateway about stream stop: %v", err)
			// Don't fail the leave operation for notification errors
		}
	}

	logger.Debugf("FSA/Onramp client %s left device %s stream %s (last viewer: %v)",
		conn.Id(), deviceID, roomInfo.Subtype, isLastViewer)
	return nil
}

// HandleClientDisconnect cleans up stream subscriptions when a client disconnects
// Uses Redis-backed session storage for cleanup and unregisters socket
func (s *Service) HandleClientDisconnect(connID string) {
	// Get connection context before removing it
	ctx, exists := s.GetConnectionContext(connID)
	if !exists {
		logger.Debugf("No context found for disconnecting client %s", connID)
		return
	}

	// Remove connection context from Redis and local storage
	s.RemoveConnectionContext(connID)

	// If this was a gateway, clean up Redis registration
	if ctx.AuthInfo != nil && ctx.AuthInfo.ClientType == domain.ClientTypeGateway {
		s.UnregisterGateway(ctx.AuthInfo.GatewayID)
		logger.Debugf("Unregistered gateway %s from Redis due to disconnect", ctx.AuthInfo.GatewayID)
	}

	// Remove all stream subscriptions for this connection
	// NOTE: streamKeysNeedingStop now contains composite keys like "device123:stream_display"
	streamKeysNeedingStop := s.streamTracker.RemoveConnection(connID)

	// Notify gateways to stop streaming for device streams that no longer have viewers
	for _, streamKey := range streamKeysNeedingStop {
		// Parse the composite key: "deviceID:streamType"
		parts := strings.Split(streamKey, ":")
		if len(parts) != 2 {
			logger.Warnf("Invalid stream key format during disconnect cleanup: %s", streamKey)
			continue
		}

		deviceID := parts[0]
		streamType := parts[1]

		if err := s.notifyGatewayStreamStop(deviceID, ctx.OrgID, streamType); err != nil {
			logger.Warnf("Failed to stop stream for device %s type %s on disconnect: %v", deviceID, streamType, err)
		}
	}

	logger.Debugf("Cleaned up %d device streams for disconnected client %s", len(streamKeysNeedingStop), connID)
}

// notifyGatewayStreamStart tells the gateway to start streaming
// Direct socket messaging to specific gateway
func (s *Service) notifyGatewayStreamStart(deviceID, orgID, streamType string) error {
	// Get gateway socket for device (database-first with caching)
	gatewaySocketID, err := s.GetGatewaySocketForDevice(deviceID)
	if err != nil {
		return fmt.Errorf("failed to find gateway for device %s: %w", deviceID, err)
	}

	// Direct start control to gateway socket
	joinCommand := map[string]interface{}{
		"action":      "start_stream",
		"device_id":   deviceID,
		"stream_type": streamType, // "stream_display" or "stream_rms"
	}

	// DIRECT SOCKET MESSAGING: Send command directly to gateway socket via gateway namespace
	gatewayNamespace := s.server.Of(domain.NamespaceGateway, nil)
	gatewayNamespace.To(socket.Room(gatewaySocketID)).Emit("stream_control", joinCommand)

	logger.Debugf("Gateway start_stream sent: device=%s, gateway_socket=%s, type=%s",
		deviceID, gatewaySocketID, streamType)
	return nil
}

// notifyGatewayStreamStartWithRetry attempts to notify gateway with retry logic for resilience
func (s *Service) notifyGatewayStreamStartWithRetry(deviceID, orgID, streamType string) {
	maxRetries := 3
	for attempt := 0; attempt < maxRetries; attempt++ {
		if err := s.notifyGatewayStreamStart(deviceID, orgID, streamType); err != nil {
			logger.Warnf("Stream start attempt %d failed for device %s: %v",
				attempt+1, deviceID, err)

			if attempt < maxRetries-1 {
				// Exponential backoff: 200ms, 400ms, 600ms
				backoff := time.Duration((attempt+1)*200) * time.Millisecond
				logger.Debugf("Retrying stream start for device %s in %v (attempt %d/%d)",
					deviceID, backoff, attempt+1, maxRetries)
				time.Sleep(backoff)
				continue
			}

			// Final attempt failed - log error but don't fail the join operation
			logger.Errorf("Failed to start stream for device %s after %d attempts: %v",
				deviceID, maxRetries, err)
			return
		}

		// Success on this attempt
		logger.Debugf("Stream start succeeded for device %s on attempt %d", deviceID, attempt+1)
		return
	}
}

// notifyGatewayStreamStop tells the gateway to stop streaming
// Direct socket messaging to specific gateway
func (s *Service) notifyGatewayStreamStop(deviceID, orgID, streamType string) error {
	// Get gateway socket for device (database-first with caching)
	gatewaySocketID, err := s.GetGatewaySocketForDevice(deviceID)
	if err != nil {
		return fmt.Errorf("failed to find gateway for device %s: %w", deviceID, err)
	}

	// Direct stop control to gateway socket
	leaveCommand := map[string]interface{}{
		"action":      "end_stream",
		"device_id":   deviceID,
		"stream_type": streamType, // "stream_display" or "stream_rms"
	}

	// DIRECT SOCKET MESSAGING: Send command directly to gateway socket via gateway namespace
	gatewayNamespace := s.server.Of(domain.NamespaceGateway, nil)
	gatewayNamespace.To(socket.Room(gatewaySocketID)).Emit("stream_control", leaveCommand)

	logger.Debugf("Gateway end_stream sent: device=%s, gateway_socket=%s, type=%s",
		deviceID, gatewaySocketID, streamType)
	return nil
}

// GetGatewaySocketForDevice returns the gateway socket ID responsible for a device
// Uses Redis-backed cache for horizontal scaling compatibility
func (s *Service) GetGatewaySocketForDevice(deviceID string) (string, error) {
	if deviceID == "" {
		return "", fmt.Errorf("device ID cannot be empty")
	}

	// Try Redis cache first (if available)
	var gatewayID string
	var err error

	if s.redisClient != nil {
		cacheKey := fmt.Sprintf("rushhour:device:%s:gateway", deviceID)
		gatewayID, err = s.redisClient.Get(s.ctx, cacheKey).Result()
		if err == redis.Nil {
			// Cache miss - will query database below
			err = nil
		} else if err != nil {
			logger.Infof("Redis error for device %s gateway lookup: %v", deviceID, err)
			// Continue to database lookup on Redis error
		} else {
			logger.Debugf("Found Redis cached gateway for device %s: %s", deviceID, gatewayID)
		}
	}

	// If not in Redis cache or Redis unavailable, query database
	if gatewayID == "" {
		gatewayID, err = s.getGatewayForDevice(deviceID)
		if err != nil {
			return "", fmt.Errorf("failed to find gateway for device %s: %w", deviceID, err)
		}

		// Cache in Redis for future requests (if available)
		if s.redisClient != nil {
			cacheKey := fmt.Sprintf("rushhour:device:%s:gateway", deviceID)
			ttl := 30 * time.Minute // Cache for 30 minutes
			if cacheErr := s.redisClient.Set(s.ctx, cacheKey, gatewayID, ttl).Err(); cacheErr != nil {
				logger.Warnf("Failed to cache device-gateway mapping in Redis: %v", cacheErr)
				// Continue without caching - not a fatal error
			} else {
				logger.Debugf("Cached device-gateway mapping in Redis: device=%s -> gateway=%s", deviceID, gatewayID)
			}
		}
	}

	// Get the socket ID for this gateway using Redis-aware lookup
	gatewaySocketID, socketErr := s.getGatewaySocketID(gatewayID)
	if socketErr != nil {
		return "", fmt.Errorf("gateway %s for device %s is not currently connected: %w", gatewayID, deviceID, socketErr)
	}

	return gatewaySocketID, nil
}

// getGatewaySocketID returns the socket ID for a gateway ID using Redis-backed lookup with retry logic
func (s *Service) getGatewaySocketID(gatewayID string) (string, error) {
	if gatewayID == "" {
		return "", fmt.Errorf("gateway ID cannot be empty")
	}

	if s.redisClient == nil {
		return "", fmt.Errorf("Redis client not available for gateway %s lookup", gatewayID)
	}

	cacheKey := fmt.Sprintf("rushhour:gateway:%s:socket", gatewayID)

	// Retry with exponential backoff for resilience against timing issues
	maxRetries := 3
	for attempt := 0; attempt < maxRetries; attempt++ {
		socketID, err := s.redisClient.Get(s.ctx, cacheKey).Result()
		if err == nil {
			logger.Debugf("Found Redis cached socket for gateway %s: %s (attempt %d)", gatewayID, socketID, attempt+1)
			return socketID, nil
		}

		if err == redis.Nil {
			// Gateway definitely not connected - no point retrying
			return "", fmt.Errorf("gateway %s is not currently connected to any instance", gatewayID)
		}

		// Redis error - retry with exponential backoff
		if attempt < maxRetries-1 {
			backoff := time.Duration((attempt+1)*100+50) * time.Millisecond
			logger.Debugf("Redis lookup failed for gateway %s, retrying in %v (attempt %d/%d): %v",
				gatewayID, backoff, attempt+1, maxRetries, err)
			time.Sleep(backoff)
			continue
		}

		// Final attempt failed
		logger.Warnf("Redis lookup failed for gateway %s after %d attempts: %v", gatewayID, maxRetries, err)
	}

	// No local fallback available - gateway must be registered in Redis for cross-instance routing
	return "", fmt.Errorf("gateway %s lookup failed after %d Redis attempts", gatewayID, maxRetries)
}

// getGatewayForDevice returns the gateway ID responsible for a device
// This function queries the database to find the SoftwareGateway.Id that manages the specified device
func (s *Service) getGatewayForDevice(deviceID string) (string, error) {
	if deviceID == "" {
		return "", fmt.Errorf("device ID cannot be empty")
	}

	// Query the Device table to get the SoftwareGatewayId for this device
	// Based on the database schema: Device.SoftwareGatewayId -> SoftwareGateway.Id
	query := `
		SELECT SoftwareGatewayId::text as gateway_id
		FROM {{Device}} 
		WHERE Id = $1 AND IsEnabled = true AND NOT IsDeleted
	`

	row, err := (*s.db).QueryRow(query, deviceID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return "", fmt.Errorf("device not found or disabled: %s", deviceID)
		}
		return "", fmt.Errorf("failed to query device-gateway mapping: %w", err)
	}

	gatewayID, ok := row["gateway_id"].(string)
	if !ok {
		return "", fmt.Errorf("device %s has invalid gateway assignment", deviceID)
	}
	if gatewayID == "" {
		return "", fmt.Errorf("device %s has no assigned gateway", deviceID)
	}

	logger.Debugf("Device %s is managed by gateway %s", deviceID, gatewayID)
	return gatewayID, nil
}

// GetStats returns service statistics
func (s *Service) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"active_streams": s.streamTracker.GetActiveDevices(),
		"stream_stats":   s.streamTracker.GetStats(),
	}
}

// createSocketIOServerWithRedis creates a Socket.IO server with Redis adapter for horizontal scaling
func createSocketIOServerWithRedis(ctx context.Context) (*socket.Server, error) {
	// Get Redis connection from RH_REDIS environment variable
	redisAddr := os.Getenv("RH_REDIS")
	if redisAddr == "" {
		logger.Warnf("RH_REDIS not set, running without Redis adapter")
		return socket.NewServer(nil, nil), nil
	}

	logger.Infof("Connecting to Redis at: %s", redisAddr)

	// Create Redis client using the official go-redis library
	redisClient := types.NewRedisClient(ctx, redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Username: "",
		Password: "",
		DB:       0,
	}))

	// Add error handling for Redis connection
	redisClient.On("error", func(a ...any) {
		logger.Errorf("Redis client error: %v", a)
	})

	// Create Socket.IO server options with Redis adapter
	config := socket.DefaultServerOptions()
	config.SetAdapter(&adapter.RedisAdapterBuilder{
		Redis: redisClient,
		Opts:  &adapter.RedisAdapterOptions{},
	})

	// Create the Socket.IO server
	server := socket.NewServer(nil, config)

	// Set up adapter room events for monitoring and debugging
	setupAdapterRoomEvents(server)

	logger.Info("Socket.IO server created with Redis adapter")
	return server, nil
}

// setupAdapterRoomEvents configures adapter room lifecycle events for monitoring and debugging
func setupAdapterRoomEvents(server *socket.Server) {
	adapter := server.Of("/", nil).Adapter()

	// Room created event - fired when first client joins a room
	adapter.On("create-room", func(args ...any) {
		if len(args) > 0 {
			room := fmt.Sprintf("%v", args[0])
			logger.Debugf("Room created: %s", room)

			// Could add metrics collection here
			// metrics.RoomCreated.Inc()
		}
	})

	// Room deleted event - fired when last client leaves a room
	adapter.On("delete-room", func(args ...any) {
		if len(args) > 0 {
			room := fmt.Sprintf("%v", args[0])
			logger.Debugf("Room deleted: %s", room)

			// Could add metrics collection here
			// metrics.RoomDeleted.Inc()
		}
	})

	// Client joined room event - fired when any client joins a room
	adapter.On("join-room", func(args ...any) {
		if len(args) > 1 {
			room := fmt.Sprintf("%v", args[0])
			socketId := fmt.Sprintf("%v", args[1])
			logger.Debugf("Socket %s joined room %s", socketId, room)

			// Could add detailed room membership tracking here
			// roomMetrics.RecordJoin(room, socketId)
		}
	})

	// Client left room event - fired when any client leaves a room
	adapter.On("leave-room", func(args ...any) {
		if len(args) > 1 {
			room := fmt.Sprintf("%v", args[0])
			socketId := fmt.Sprintf("%v", args[1])
			logger.Debugf("Socket %s left room %s", socketId, room)

			// Could add detailed room membership tracking here
			// roomMetrics.RecordLeave(room, socketId)
		}
	})

	logger.Debug("Adapter room events configured for monitoring")
}
