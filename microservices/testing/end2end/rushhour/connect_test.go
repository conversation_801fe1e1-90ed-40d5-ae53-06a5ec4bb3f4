package end2end

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/zishang520/engine.io-client-go/transports"
	"github.com/zishang520/engine.io/v2/types"
	"github.com/zishang520/socket.io-client-go/socket"

	Utils "synapse-its.com/testing/utils"
)

// TestRushHourConnectionFailures verifies RH emits error messages and disconnects
// when clients attempt to connect without required authentication data.
func TestRushHourConnectionFailures(t *testing.T) {
	// Ensure RushHour is up
	ctx := context.Background()
	err := Utils.AwaitRushHour(ctx, time.Second)
	require.NoError(t, err, "RushHour should be ready")

	t.Run("FSA missing token emits error and disconnects", func(t *testing.T) {
		opts := socket.DefaultOptions()
		// Explicitly allow WebSocket and Polling; not allowing QUIC/WebTransport
		opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))

		// Provide empty auth map (no token) to trigger 'missing token'
		opts.SetAuth(map[string]interface{}{})

		manager := socket.NewManager("http://rushhour:8080", opts)
		client := manager.Socket("/auth/fsa", opts)
		defer client.Disconnect()

		errMsgCh := make(chan string, 1)
		discCh := make(chan struct{}, 1)

		client.On("error", func(args ...any) {
			if len(args) > 0 {
				if s, ok := args[0].(string); ok {
					select {
					case errMsgCh <- s:
					default:
					}
				}
			}
		})
		client.On("disconnect", func(args ...any) {
			select {
			case discCh <- struct{}{}:
			default:
			}
		})

		client.Connect()

		var gotErr string
		select {
		case gotErr = <-errMsgCh:
			// ok
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for error event from RH")
		}
		require.Equal(t, "missing token", gotErr)

		select {
		case <-discCh:
			// ok
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for disconnect after error")
		}
	})

	t.Run("Gateway missing credentials emits error and disconnects", func(t *testing.T) {
		opts := socket.DefaultOptions()
		// Explicitly allow WebSocket and Polling; not allowing QUIC/WebTransport
		opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))

		// Provide empty auth map (no machine_key/api_key) to trigger error
		opts.SetAuth(map[string]interface{}{})

		manager := socket.NewManager("http://rushhour:8080", opts)
		client := manager.Socket("/auth/gateway", opts)
		defer client.Disconnect()

		errMsgCh := make(chan string, 1)
		connErrCh := make(chan string, 1)
		discCh := make(chan struct{}, 1)

		client.On("error", func(args ...any) {
			if len(args) > 0 {
				if s, ok := args[0].(string); ok {
					select {
					case errMsgCh <- s:
					default:
					}
				} else {
					select {
					case errMsgCh <- "error":
					default:
					}
				}
			}
		})
		client.On("connect_error", func(args ...any) {
			if len(args) > 0 {
				if s, ok := args[0].(string); ok {
					select {
					case connErrCh <- s:
					default:
					}
				} else {
					select {
					case connErrCh <- "connect_error":
					default:
					}
				}
			} else {
				select {
				case connErrCh <- "connect_error":
				default:
				}
			}
		})
		client.On("disconnect", func(args ...any) {
			select {
			case discCh <- struct{}{}:
			default:
			}
		})

		client.Connect()

		var gotErr string
		select {
		case gotErr = <-errMsgCh:
			// ok
		case gotErr = <-connErrCh:
			// accept connect_error
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for error event from RH")
		}
		if gotErr != "connect_error" {
			require.Equal(t, "missing machine_key or api_key", gotErr)
		}

		select {
		case <-discCh:
			// ok
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for disconnect after error")
		}
	})
}

// TestRushHourFSAErrorOnForbiddenFields verifies that after a successful FSA connection,
// sending a device_request with forbidden fields (SessionId) results in an error event.
func TestRushHourFSAErrorOnForbiddenFields(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	err := Utils.AwaitRushHour(ctx, time.Second)
	require.NoError(t, err, "RushHour should be ready")

	// Authenticate FSA
	jwtToken := Utils.PerformJWTAuthentication(t, testUsername, testPassword)
	require.NotEmpty(t, jwtToken)

	opts := socket.DefaultOptions()
	opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))
	opts.SetAuth(map[string]interface{}{"token": jwtToken})

	manager := socket.NewManager("http://rushhour:8080", opts)
	client := manager.Socket("/auth/fsa", opts)
	defer client.Disconnect()

	connected := make(chan struct{}, 1)
	authed := make(chan struct{}, 1)
	errCh := make(chan string, 1)

	client.On("connect", func(args ...any) {
		select {
		case connected <- struct{}{}:
		default:
		}
	})
	client.On("auth_success", func(args ...any) {
		select {
		case authed <- struct{}{}:
		default:
		}
	})
	client.On("error", func(args ...any) {
		if len(args) > 0 {
			if s, ok := args[0].(string); ok {
				select {
				case errCh <- s:
				default:
				}
			} else {
				select {
				case errCh <- "error":
				default:
				}
			}
		}
	})

	client.Connect()
	select {
	case <-connected:
	case <-time.After(10 * time.Second):
		t.Fatal("FSA connect timeout")
	}
	select {
	case <-authed:
	case <-time.After(10 * time.Second):
		t.Fatal("FSA auth timeout")
	}

	// Send a forbidden device_request with SessionId set (should be rejected by RH)
	bad := map[string]interface{}{
		"type":       1, // ENVELOPE_WRAPPER_COMMAND
		"request_id": 1,
		"device_id":  testDeviceID,
		"origin":     1, // ORIGIN_FSA
		"session_id": "should-not-be-set",
	}
	client.Emit("device_request", bad)

	// Expect an error from the server
	select {
	case msg := <-errCh:
		// Accept the specific error message from service validation
		// session id must not be set by client for device_request
		require.Contains(t, msg, "session id must not be set")
	case <-time.After(5 * time.Second):
		t.Fatal("Timeout waiting for error after forbidden device_request")
	}
}
