/*
RushHour E2E Streaming & Recovery Tests

This file contains end-to-end tests for the streaming functionality and connection resilience
of the rushhour microservice. These tests validate Socket.IO connectivity, authentication flows,
and system recovery capabilities for both FSA and Gateway clients.

Test Coverage:
- Basic Socket.IO connectivity validation
- FSA authentication using JWT tokens (via broker)
- Gateway authentication using machine_key/api_key pairs
- Connection resilience and recovery scenarios
- Socket.IO v4 protocol compatibility
- HTTP polling transport reliability

Test Functions:
1. TestRushHourStreaming:
  - Basic Gateway Connection: Validates gateway can connect and authenticate
  - Basic FSA Connection: Validates FSA can connect and authenticate with JWT

2. TestRushHourGatewayRecovery:
  - GatewayRecoveryResilience: Tests FSA connection stability and recovery

Key Validation Points:
- Socket.IO handshake and connection establishment
- Namespace-based authentication (/auth/gateway, /auth/fsa)
- Authentication event handling (gateway_init, auth_success)
- Connection state management and cleanup
- Service availability and readiness

Technical Implementation:
- Uses github.com/zishang520/socket.io-client-go library for Socket.IO client connections
- Eliminates WebSocket connectivity issues in test environments
- Provides foundation for future streaming data validation
- Validates authentication prerequisites for messaging tests

Dependencies:
- Broker service (for JWT token generation)
- RushHour service (Socket.IO server)
- PostgreSQL (for credential validation)
- Redis (for Socket.IO adapter and session management)
*/
package end2end

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/zishang520/engine.io-client-go/transports"
	"github.com/zishang520/engine.io/v2/types"
	"github.com/zishang520/socket.io-client-go/socket"
	"google.golang.org/protobuf/proto"

	cmdresprealtime "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_realtime"
	monfrealtime "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/realtime"
	monfwrappers "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/wrappers"
	rushhourv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/rushhour/v1"
	Utils "synapse-its.com/testing/utils"
)

// StreamingData represents a data point in the stream
type StreamingData struct {
	Timestamp  time.Time              `json:"timestamp"`
	SequenceID int                    `json:"sequence_id"`
	DeviceID   string                 `json:"device_id"`
	DataType   string                 `json:"data_type"`
	Payload    map[string]interface{} `json:"payload"`
	StreamID   string                 `json:"stream_id"`
}

// streamControlMsg captures simplified stream_control notifications to the gateway
type streamControlMsg struct {
	action     string
	deviceID   string
	streamType string
}

// connectGatewayClientAuth connects a gateway client and returns the client with
// channels for connection and authentication completion.
func connectGatewayClientAuth(t *testing.T) (*socket.Socket, <-chan bool, <-chan bool) {
	opts := socket.DefaultOptions()
	opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))
	opts.SetAuth(map[string]interface{}{
		"machine_key": testMachineKey,
		"api_key":     testAPIKey,
	})

	manager := socket.NewManager("http://rushhour:8080", opts)
	client := manager.Socket("/auth/gateway", opts)

	connected := make(chan bool, 1)
	authenticated := make(chan bool, 1)

	client.On("connect", func(args ...any) { connected <- true })
	client.On("gateway_init", func(args ...any) { authenticated <- true })
	client.On("connect_error", func(args ...any) { t.Logf("Gateway: Connection error: %v", args) })

	client.Connect()
	return client, connected, authenticated
}

// connectFSAClientForStreaming connects an FSA client and sets up a device_message
// listener that delivers MonitorDisplayData payloads on a channel.
func connectFSAClientForStreaming(t *testing.T, jwtToken string) (*socket.Socket, <-chan bool, <-chan bool, <-chan *monfrealtime.MonitorDisplayData) {
	opts := socket.DefaultOptions()
	opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))
	opts.SetAuth(map[string]interface{}{"token": jwtToken})

	manager := socket.NewManager("http://rushhour:8080", opts)
	client := manager.Socket("/auth/fsa", opts)

	connected := make(chan bool, 1)
	authenticated := make(chan bool, 1)
	displayReceived := make(chan *monfrealtime.MonitorDisplayData, 1)

	client.On("connect", func(args ...any) { connected <- true })
	client.On("auth_success", func(args ...any) { authenticated <- true })
	client.On("connect_error", func(args ...any) { t.Logf("FSA: Connection error: %v", args) })
	client.On("device_message", func(args ...any) {
		if len(args) == 0 {
			return
		}
		var env rushhourv1.SocketEnvelope
		switch v := args[0].(type) {
		case string:
			if err := json.Unmarshal([]byte(v), &env); err != nil {
				return
			}
		case map[string]interface{}:
			b, _ := json.Marshal(v)
			if err := json.Unmarshal(b, &env); err != nil {
				return
			}
		case []byte:
			if err := proto.Unmarshal(v, &env); err != nil {
				return
			}
		default:
			return
		}
		if env.Type != rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_RESPONSE || len(env.Payload) == 0 {
			return
		}
		var wr monfwrappers.WrapperResponse
		if err := proto.Unmarshal(env.Payload, &wr); err != nil {
			return
		}
		if disp := wr.GetRealtimeDisplay(); disp != nil && disp.GetMonitorDisplay() != nil {
			displayReceived <- disp.GetMonitorDisplay()
		}
	})

	client.Connect()
	return client, connected, authenticated, displayReceived
}

// startGatewayStreamer starts a lightweight gateway client that listens for device_request,
// echoes OrganizationId on orgIDCh, and emits two stream_display frames when roomReadyCh is signaled.
// It also exposes channels for stream_control start/end.
func startGatewayStreamer(t *testing.T, roomReadyCh <-chan struct{}, orgIDCh chan<- string) (*socket.Socket, <-chan bool, <-chan streamControlMsg, <-chan streamControlMsg) {
	gwOpts := socket.DefaultOptions()
	gwOpts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))
	gwOpts.SetAuth(map[string]interface{}{
		"machine_key": testMachineKey,
		"api_key":     testAPIKey,
	})
	gwMgr := socket.NewManager("http://rushhour:8080", gwOpts)
	gwClient := gwMgr.Socket("/auth/gateway", gwOpts)

	gwReady := make(chan bool, 1)
	startCtlCh := make(chan streamControlMsg, 1)
	endCtlCh := make(chan streamControlMsg, 1)

	gwClient.On("gateway_init", func(args ...any) { gwReady <- true })
	gwClient.On("stream_control", func(args ...any) {
		if len(args) == 0 {
			return
		}
		var action, deviceID, streamType string
		switch v := args[0].(type) {
		case map[string]interface{}:
			if a, ok := v["action"].(string); ok {
				action = a
			}
			if d, ok := v["device_id"].(string); ok {
				deviceID = d
			}
			if s, ok := v["stream_type"].(string); ok {
				streamType = s
			}
		case string:
			var m map[string]interface{}
			if err := json.Unmarshal([]byte(v), &m); err == nil {
				if a, ok := m["action"].(string); ok {
					action = a
				}
				if d, ok := m["device_id"].(string); ok {
					deviceID = d
				}
				if s, ok := m["stream_type"].(string); ok {
					streamType = s
				}
			}
		}
		msg := streamControlMsg{action: action, deviceID: deviceID, streamType: streamType}
		if action == "start_stream" {
			select {
			case startCtlCh <- msg:
			default:
			}
		} else if action == "end_stream" {
			select {
			case endCtlCh <- msg:
			default:
			}
		}
	})
	gwClient.On("device_request", func(args ...any) {
		if len(args) == 0 {
			return
		}
		var env rushhourv1.SocketEnvelope
		switch v := args[0].(type) {
		case string:
			if err := json.Unmarshal([]byte(v), &env); err != nil {
				return
			}
		case map[string]interface{}:
			b, _ := json.Marshal(v)
			if err := json.Unmarshal(b, &env); err != nil {
				return
			}
		case []byte:
			if err := proto.Unmarshal(v, &env); err != nil {
				return
			}
		default:
			return
		}
		select {
		case orgIDCh <- env.GetOrganizationId():
		default:
		}

		// Build and emit two display frames (broadcast path: no SessionId)
		disp := func(seq int, text string) *monfrealtime.MonitorDisplayData {
			return &monfrealtime.MonitorDisplayData{
				EnableFieldDisplay: true,
				DisplayChannels:    monfrealtime.EDisplayChannelsType_DISP_CH_16,
				LineCount:          1,
				DisplayLines:       []*monfrealtime.LcdDisplayLine{{LineNumber: 1, LineText: []byte(text)}},
			}
		}
		buildResp := func(seq int, md *monfrealtime.MonitorDisplayData) *rushhourv1.SocketEnvelope {
			wr := &monfwrappers.WrapperResponse{
				Version:   1,
				RequestId: env.GetRequestId(),
				Code:      monfwrappers.EResponseCodes_RESP_SUCCESS,
				Response:  &monfwrappers.WrapperResponse_RealtimeDisplay{RealtimeDisplay: &cmdresprealtime.RealtimeDisplay1{SequenceNumber: uint32(seq), MonitorDisplay: md}},
			}
			wrBytes, err := proto.Marshal(wr)
			if err != nil {
				return nil
			}
			return &rushhourv1.SocketEnvelope{
				Type:           rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_RESPONSE,
				RequestId:      env.GetRequestId(),
				SessionId:      "",
				DeviceId:       env.GetDeviceId(),
				OrganizationId: env.GetOrganizationId(),
				Origin:         rushhourv1.OriginType_ORIGIN_GATEWAY,
				Payload:        wrBytes,
			}
		}

		go func() {
			select {
			case <-roomReadyCh:
				gwClient.Emit("stream_display", buildResp(1, disp(1, "Streaming OK #1")))
				time.Sleep(100 * time.Millisecond)
				gwClient.Emit("stream_display", buildResp(2, disp(2, "Streaming OK #2")))
			case <-time.After(5 * time.Second):
				gwClient.Emit("stream_display", buildResp(1, disp(1, "Streaming OK #1")))
				time.Sleep(100 * time.Millisecond)
				gwClient.Emit("stream_display", buildResp(2, disp(2, "Streaming OK #2")))
			}
		}()
	})

	gwClient.Connect()
	return gwClient, gwReady, startCtlCh, endCtlCh
}

// TestRushHourStreaming tests streaming functionality using the new simple client
func TestRushHourStreaming(t *testing.T) {
	// Wait for services to be ready
	ctx := context.Background()
	err := Utils.AwaitRushHour(ctx, time.Second)
	require.NoError(t, err, "RushHour should be ready")

	t.Run("Basic Gateway Connection", func(t *testing.T) {
		t.Log("Testing basic gateway connection for streaming...")
		gwClient, gwConnected, gwAuthed := connectGatewayClientAuth(t)
		defer gwClient.Disconnect()

		select {
		case <-gwConnected:
			t.Log("[OK] Gateway connection successful")
		case <-time.After(15 * time.Second):
			t.Fatal("[FAIL] Gateway connection timeout")
		}
		select {
		case <-gwAuthed:
			t.Log("[OK] Gateway authentication successful")
		case <-time.After(10 * time.Second):
			t.Fatal("[FAIL] Gateway authentication timeout")
		}
	})

	t.Run("Basic FSA Connection", func(t *testing.T) {
		t.Log("Testing basic FSA connection for streaming...")

		// Get JWT token
		jwtToken := Utils.PerformJWTAuthentication(t, testUsername, testPassword)
		require.NotEmpty(t, jwtToken, "Should receive valid JWT token")

		// Create FSA client via helper
		client, connected, authenticated, displayReceived := connectFSAClientForStreaming(t, jwtToken)
		defer client.Disconnect()

		// Spin up a lightweight gateway client to broadcast display frames
		orgIDCh := make(chan string, 1)
		roomReadyCh := make(chan struct{}, 1)
		gwClient, gwReady, startCtlCh, endCtlCh := startGatewayStreamer(t, roomReadyCh, orgIDCh)
		defer gwClient.Disconnect()
		select {
		case <-gwReady:
			// gateway authenticated
		case <-time.After(10 * time.Second):
			t.Fatal("[FAIL] Gateway authentication timeout")
		}

		// Connect
		client.Connect()

		// Wait for connection
		select {
		case <-connected:
			t.Log("[OK] FSA connection successful")
		case <-time.After(15 * time.Second):
			t.Fatal("[FAIL] FSA connection timeout")
		}

		// Wait for authentication
		select {
		case <-authenticated:
			t.Log("[OK] FSA authentication successful")
		case <-time.After(10 * time.Second):
			t.Fatal("[FAIL] FSA authentication timeout")
		}

		// Trigger gateway to emit one display frame by sending a start_realtime command request
		start := &cmdresprealtime.CmdStartRealtimeData{
			SendMonitorDisplay:           true,
			MonitorDisplayChangedOnly:    false,
			SendIntervalMs:               100,
			UpdateFullDisplayDataSeconds: 0,
		}
		wc := &monfwrappers.WrapperCommand{
			Version:   1,
			RequestId: 777,
			Command:   &monfwrappers.WrapperCommand_StartRealtime{StartRealtime: start},
		}
		payload, err := proto.Marshal(wc)
		require.NoError(t, err)
		env := &rushhourv1.SocketEnvelope{
			Type:      rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_COMMAND,
			RequestId: wc.GetRequestId(),
			DeviceId:  testDeviceID,
			Origin:    rushhourv1.OriginType_ORIGIN_FSA,
			Payload:   payload,
		}
		client.Emit("device_request", env)

		// Wait for the gateway-provided org ID from the device_request envelope
		var gatewayOrgID string
		select {
		case gatewayOrgID = <-orgIDCh:
			// ok
		case <-time.After(5 * time.Second):
			t.Fatal("[FAIL] Timed out waiting for gateway org ID")
		}
		// Join broadcast stream room using the programmatically discovered gateway org ID
		room := fmt.Sprintf("org:%s:device:%s:stream_display", gatewayOrgID, testDeviceID)
		client.Emit("join", room)
		// Ensure join is processed before gateway broadcast
		time.Sleep(100 * time.Millisecond)
		roomReadyCh <- struct{}{}

		// Expect start_stream control on gateway
		select {
		case msg := <-startCtlCh:
			require.Equal(t, "start_stream", msg.action)
			require.Equal(t, testDeviceID, msg.deviceID)
			require.Equal(t, "stream_display", msg.streamType)
		case <-time.After(5 * time.Second):
			t.Fatal("[FAIL] Did not receive start_stream control message")
		}

		// Expect two display frames via device_message
		received := 0
		deadline := time.After(10 * time.Second)
		for received < 2 {
			select {
			case disp := <-displayReceived:
				require.NotNil(t, disp)
				received++
				t.Logf("[OK] Received display frame %d (lines=%d chType=%v)", received, disp.GetLineCount(), disp.GetDisplayChannels())
			case <-deadline:
				t.Fatal("[FAIL] Did not receive two streaming display frames")
			}
		}

		// Leave the room and expect end_stream control on gateway
		client.Emit("leave", room)
		select {
		case msg := <-endCtlCh:
			require.Equal(t, "end_stream", msg.action)
			require.Equal(t, testDeviceID, msg.deviceID)
			require.Equal(t, "stream_display", msg.streamType)
		case <-time.After(5 * time.Second):
			t.Fatal("[FAIL] Did not receive end_stream control message")
		}
	})
}

// TestRushHourGatewayRecovery tests gateway recovery functionality
func TestRushHourGatewayRecovery(t *testing.T) {
	// Wait for services to be ready
	ctx := context.Background()
	err := Utils.AwaitRushHour(ctx, time.Second)
	require.NoError(t, err, "RushHour should be ready")

	t.Run("GatewayRecoveryResilience", func(t *testing.T) {
		t.Log("Testing gateway recovery and reconnection...")

		// Get JWT token
		jwtToken := Utils.PerformJWTAuthentication(t, testUsername, testPassword)
		require.NotEmpty(t, jwtToken, "Should receive valid JWT token")

		// Create FSA client with zishang520 library
		opts := socket.DefaultOptions()
		// Explicitly allow WebSocket and Polling; not allowing QUIC/WebTransport
		opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))

		// Add JWT token to the auth object (server expects this)
		authData := map[string]interface{}{
			"token": jwtToken,
		}
		opts.SetAuth(authData)
		// Create manager and connect to FSA namespace
		manager := socket.NewManager("http://rushhour:8080", opts)
		client := manager.Socket("/auth/fsa", opts)
		defer client.Disconnect()

		// Set up connection handler
		connected := make(chan bool, 1)
		authenticated := make(chan bool, 1)

		client.On("connect", func(args ...any) {
			t.Log("FSA: [OK] Connected to Socket.IO for recovery test")
			connected <- true
		})

		client.On("auth_success", func(args ...any) {
			t.Logf("FSA: [OK] Authenticated successfully: %v", args)
			authenticated <- true
		})

		client.On("connect_error", func(args ...any) {
			t.Errorf("FSA: Connection error: %v", args)
		})

		// Connect
		client.Connect()
		t.Log("FSA: Initiating connection...")

		// Wait for connection
		select {
		case <-connected:
			t.Log("[OK] FSA connection successful")
		case <-time.After(15 * time.Second):
			t.Fatal("[FAIL] FSA connection timeout")
		}

		// Wait for authentication
		select {
		case <-authenticated:
			t.Log("[OK] FSA authentication successful - recovery test passed")
		case <-time.After(10 * time.Second):
			t.Fatal("[FAIL] FSA authentication timeout")
		}
	})
}
