-- Migration for logPreviousFail table to add missing T12VDCInput and HDSPSignalVoltages fields
-- CAT-544: Add fields that were missing from the BigQuery schema when upgrading from 0.0 to 1.0

-- This migration only applies when upgrading from schema 0.0 to 1.0
-- The 1.0 DDL already includes these fields, so this migration fills the gap

-- Step 1: Create backup table  
CREATE TABLE {{logPreviousFail_backup}} AS
SELECT * FROM {{logPreviousFail}};

-- Step 2: Drop the original table
DROP TABLE {{logPreviousFail}};

-- Step 3: Recreate table with new schema including the additional fields
CREATE TABLE {{logPreviousFail}} (
  organizationidentifier  STRING,
  softwaregatewayid       STRING,
  tz                      STRING,
  topic                   STRING,
  pubsubtimestamp         TIMESTAMP,
  pubsubid                STRING,
  deviceid                STRING,
  header                  STRUCT<
    commversion      STRING,
    model            INT64,
    firmwareversion  STRING,
    firmwarerevision STRING,
    monitorid        INT64,
    volt220          BOOL,
    voltdc           BOOL,
    mainsdc          BOOL,
    powerdownlevel   INT64,
    blackoutlevel    INT64,
    maxchannels      INT64
  >,
  devicemodel             STRING,
  records ARRAY<STRUCT<
    datetime                          TIMESTAMP,
    fault                             STRING,
    acline                            STRING,
    t48vdcsignalbus                   STRING,
    redenable                         STRING,
    mccoilee                          STRING,
    specialfunction1                  STRING,
    specialfunction2                  STRING,
    wdtmonitor                        STRING,
    t24vdcinput                       STRING,
    t12vdcinput                       STRING,
    temperature                       INT64,
    lsflashbit                        BOOL,
    faultstatus                       ARRAY<BOOL>,
    channelgreenstatus                ARRAY<BOOL>,
    channelyellowstatus               ARRAY<BOOL>,
    channelredstatus                  ARRAY<BOOL>,
    channelwalkstatus                 ARRAY<BOOL>,
    channelgreenfieldcheckstatus      ARRAY<BOOL>,
    channelyellowfieldcheckstatus     ARRAY<BOOL>,
    channelredfieldcheckstatus        ARRAY<BOOL>,
    channelwalkfieldcheckstatus       ARRAY<BOOL>,
    channelgreenrecurrentpulsestatus  ARRAY<BOOL>,
    channelyellowrecurrentpulsestatus ARRAY<BOOL>,
    channelredrecurrentpulsestatus    ARRAY<BOOL>,
    channelwalkrecurrentpulsestatus   ARRAY<BOOL>,
    channelgreenrmsvoltage            ARRAY<INT64>,
    channelyellowrmsvoltage           ARRAY<INT64>,
    channelredrmsvoltage              ARRAY<INT64>,
    channelwalkrmsvoltage             ARRAY<INT64>,
    hdspsignalvoltages                ARRAY<INT64>,
    nextconflictingchannels           ARRAY<BOOL>,
    channelredcurrentstatus           ARRAY<BOOL>,
    channelyellowcurrentstatus        ARRAY<BOOL>,
    channelgreencurrentstatus         ARRAY<BOOL>,
    channelredrmscurrent              ARRAY<INT64>,
    channelyellowrmscurrent           ARRAY<INT64>,
    channelgreenrmscurrent            ARRAY<INT64>
  >>,
  rawmessage              BYTES,
  loguuid                 STRING
)
{% PARTITION BY DATE(pubsubtimestamp)
 CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};

-- Step 4: Restore data from backup - explicitly map fields and add NULL for new ones
{% 
INSERT INTO {{logPreviousFail}} (
  organizationidentifier,
  softwaregatewayid,
  tz,
  topic,
  pubsubtimestamp,
  pubsubid,
  deviceid,
  header,
  devicemodel,
  records,
  rawmessage,
  loguuid
)
SELECT
  b.organizationidentifier,
  b.softwaregatewayid,
  b.tz,
  b.topic,
  b.pubsubtimestamp,
  b.pubsubid,
  b.deviceid,
  b.header,
  b.devicemodel,
  ARRAY(
    SELECT AS STRUCT
      r.datetime                          AS datetime,
      r.fault                             AS fault,
      r.acline                            AS acline,
      r.t48vdcsignalbus                   AS t48vdcsignalbus,
      r.redenable                         AS redenable,
      r.mccoilee                          AS mccoilee,
      r.specialfunction1                  AS specialfunction1,
      r.specialfunction2                  AS specialfunction2,
      r.wdtmonitor                        AS wdtmonitor,
      r.t24vdcinput                       AS t24vdcinput,
      CAST(NULL AS STRING)                AS t12vdcinput,                   -- NEW
      r.temperature                       AS temperature,
      r.lsflashbit                        AS lsflashbit,
      r.faultstatus                       AS faultstatus,
      r.channelgreenstatus                AS channelgreenstatus,
      r.channelyellowstatus               AS channelyellowstatus,
      r.channelredstatus                  AS channelredstatus,
      r.channelwalkstatus                 AS channelwalkstatus,
      r.channelgreenfieldcheckstatus      AS channelgreenfieldcheckstatus,
      r.channelyellowfieldcheckstatus     AS channelyellowfieldcheckstatus,
      r.channelredfieldcheckstatus        AS channelredfieldcheckstatus,
      r.channelwalkfieldcheckstatus       AS channelwalkfieldcheckstatus,
      r.channelgreenrecurrentpulsestatus  AS channelgreenrecurrentpulsestatus,
      r.channelyellowrecurrentpulsestatus AS channelyellowrecurrentpulsestatus,
      r.channelredrecurrentpulsestatus    AS channelredrecurrentpulsestatus,
      r.channelwalkrecurrentpulsestatus   AS channelwalkrecurrentpulsestatus,
      r.channelgreenrmsvoltage            AS channelgreenrmsvoltage,
      r.channelyellowrmsvoltage           AS channelyellowrmsvoltage,
      r.channelredrmsvoltage              AS channelredrmsvoltage,
      r.channelwalkrmsvoltage             AS channelwalkrmsvoltage,
      CAST(NULL AS ARRAY<INT64>)          AS hdspsignalvoltages,            -- NEW
      r.nextconflictingchannels           AS nextconflictingchannels,
      r.channelredcurrentstatus           AS channelredcurrentstatus,
      r.channelyellowcurrentstatus        AS channelyellowcurrentstatus,
      r.channelgreencurrentstatus         AS channelgreencurrentstatus,
      r.channelredrmscurrent              AS channelredrmscurrent,
      r.channelyellowrmscurrent           AS channelyellowrmscurrent,
      r.channelgreenrmscurrent            AS channelgreenrmscurrent
    FROM UNNEST(b.records) AS r
  ) AS records,
  b.rawmessage,
  b.loguuid
FROM {{logPreviousFail_backup}} AS b;
%}

-- Step 5: Clean up backup table
DROP TABLE {{logPreviousFail_backup}};
